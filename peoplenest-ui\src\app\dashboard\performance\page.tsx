"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  TrendingUp,
  Target,
  Award,
  Calendar,
  Users,
  Star,
  BarChart3,
  <PERSON><PERSON>hart,
  Download,
  Plus,
  Filter,
  Search,
  Eye,
  Edit,
  MessageSquare,
  CheckCircle,
  AlertTriangle,
  Clock,
  User,
  Building
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LineChart, Line, AreaChart, Area, BarChart, Bar, Pie<PERSON>hart as RechartsPieChart, Cell, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts'

// Mock performance data
const performanceData = [
  {
    id: 1,
    employeeId: 1,
    employeeName: "<PERSON>",
    department: "Engineering",
    position: "Senior Software Engineer",
    currentRating: 4.8,
    previousRating: 4.6,
    goals: 8,
    goalsCompleted: 7,
    reviewDate: "2024-01-15",
    nextReviewDate: "2024-07-15",
    status: "completed",
    feedback: "Excellent performance with strong technical leadership",
    avatar: "/avatars/sarah.jpg"
  },
  {
    id: 2,
    employeeId: 2,
    employeeName: "Mike Chen",
    department: "Product",
    position: "Product Manager",
    currentRating: 4.6,
    previousRating: 4.4,
    goals: 6,
    goalsCompleted: 5,
    reviewDate: "2024-01-20",
    nextReviewDate: "2024-07-20",
    status: "in-progress",
    feedback: "Strong product vision and stakeholder management",
    avatar: "/avatars/mike.jpg"
  },
  {
    id: 3,
    employeeId: 3,
    employeeName: "Emily Davis",
    department: "Design",
    position: "UX Designer",
    currentRating: 4.9,
    previousRating: 4.7,
    goals: 5,
    goalsCompleted: 5,
    reviews: 4,
    reviewDate: "2024-01-10",
    nextReviewDate: "2024-07-10",
    status: "completed",
    feedback: "Outstanding design work with excellent user research",
    avatar: "/avatars/emily.jpg"
  },
  {
    id: 4,
    employeeId: 4,
    employeeName: "David Wilson",
    department: "Engineering",
    position: "DevOps Engineer",
    currentRating: 4.4,
    previousRating: 4.2,
    goals: 7,
    goalsCompleted: 4,
    reviewDate: null,
    nextReviewDate: "2024-02-15",
    status: "pending",
    feedback: null,
    avatar: "/avatars/david.jpg"
  }
]

// Performance trends data
const performanceTrends = [
  { month: 'Jan', avgRating: 4.2, goalCompletion: 78 },
  { month: 'Feb', avgRating: 4.3, goalCompletion: 82 },
  { month: 'Mar', avgRating: 4.4, goalCompletion: 85 },
  { month: 'Apr', avgRating: 4.5, goalCompletion: 88 },
  { month: 'May', avgRating: 4.6, goalCompletion: 90 },
  { month: 'Jun', avgRating: 4.7, goalCompletion: 92 }
]

// Department performance data
const departmentPerformance = [
  { name: 'Engineering', rating: 4.6, employees: 12 },
  { name: 'Product', rating: 4.4, employees: 8 },
  { name: 'Design', rating: 4.8, employees: 6 },
  { name: 'Sales', rating: 4.3, employees: 10 },
  { name: 'Marketing', rating: 4.5, employees: 7 }
]

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

const performanceSummary = {
  totalEmployees: performanceData.length,
  avgRating: (performanceData.reduce((sum, p) => sum + p.currentRating, 0) / performanceData.length).toFixed(1),
  completed: performanceData.filter(p => p.status === "completed").length,
  inProgress: performanceData.filter(p => p.status === "in-progress").length,
  pending: performanceData.filter(p => p.status === "pending").length,
  totalGoals: performanceData.reduce((sum, p) => sum + p.goals, 0),
  completedGoals: performanceData.reduce((sum, p) => sum + p.goalsCompleted, 0)
}

export default function PerformancePage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [selectedDepartment, setSelectedDepartment] = useState("All")
  const [viewMode, setViewMode] = useState<"overview" | "reviews" | "goals">("overview")

  const filteredPerformance = performanceData.filter(perf => {
    const matchesSearch = perf.employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         perf.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         perf.position.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = selectedStatus === "All" || perf.status === selectedStatus
    const matchesDepartment = selectedDepartment === "All" || perf.department === selectedDepartment
    
    return matchesSearch && matchesStatus && matchesDepartment
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "success"
      case "in-progress": return "warning"
      case "pending": return "destructive"
      default: return "secondary"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed": return <CheckCircle className="h-4 w-4" />
      case "in-progress": return <Clock className="h-4 w-4" />
      case "pending": return <AlertTriangle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600"
    if (rating >= 4.0) return "text-blue-600 dark:text-blue-400"
    if (rating >= 3.5) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Performance Management"
        subtitle={`Tracking performance for ${performanceSummary.totalEmployees} employees`}
        actions={
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export Report
            </Button>
            <Button size="sm">
              <Plus className="w-4 h-4 mr-2" />
              New Review
            </Button>
          </div>
        }
      />

      {/* Performance Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Star className="h-8 w-8 text-yellow-500 dark:text-yellow-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{performanceSummary.avgRating}</p>
                <p className="text-sm text-muted-foreground">Average Rating</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">
                  {Math.round((performanceSummary.completedGoals / performanceSummary.totalGoals) * 100)}%
                </p>
                <p className="text-sm text-muted-foreground">Goal Completion</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{performanceSummary.completed}</p>
                <p className="text-sm text-muted-foreground">Reviews Completed</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-orange-600 dark:text-orange-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{performanceSummary.pending}</p>
                <p className="text-sm text-muted-foreground">Pending Reviews</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Performance Trends</span>
            </CardTitle>
            <CardDescription>Average rating and goal completion over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" domain={[3.5, 5]} />
                <YAxis yAxisId="right" orientation="right" domain={[0, 100]} />
                <Tooltip />
                <Legend />
                <Line yAxisId="left" type="monotone" dataKey="avgRating" stroke="#8884d8" strokeWidth={2} name="Avg Rating" />
                <Line yAxisId="right" type="monotone" dataKey="goalCompletion" stroke="#82ca9d" strokeWidth={2} name="Goal Completion %" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Department Performance</span>
            </CardTitle>
            <CardDescription>Average performance rating by department</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={departmentPerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[3.5, 5]} />
                <Tooltip />
                <Bar dataKey="rating" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* View Mode Tabs */}
      <div className="flex items-center space-x-2">
        <Button
          variant={viewMode === "overview" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("overview")}
        >
          Overview
        </Button>
        <Button
          variant={viewMode === "reviews" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("reviews")}
        >
          Reviews
        </Button>
        <Button
          variant={viewMode === "goals" ? "default" : "outline"}
          size="sm"
          onClick={() => setViewMode("goals")}
        >
          Goals
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by employee name, department, or position..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                aria-label="Filter by status"
              >
                <option value="All">All Status</option>
                <option value="completed">Completed</option>
                <option value="in-progress">In Progress</option>
                <option value="pending">Pending</option>
              </select>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                aria-label="Filter by department"
              >
                <option value="All">All Departments</option>
                <option value="Engineering">Engineering</option>
                <option value="Product">Product</option>
                <option value="Design">Design</option>
                <option value="Sales">Sales</option>
                <option value="Marketing">Marketing</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredPerformance.length} of {performanceData.length} performance records
        </p>
      </div>

      {/* Performance Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/50 border-b border-border">
                <tr>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Employee</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Department</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Current Rating</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Goals Progress</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Last Review</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Next Review</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Status</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPerformance.map((performance, index) => (
                  <motion.tr
                    key={performance.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.05 }}
                    className="border-b border-border hover:bg-muted/50"
                  >
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={performance.avatar} alt={performance.employeeName} />
                          <AvatarFallback>
                            {performance.employeeName.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-foreground">{performance.employeeName}</p>
                          <p className="text-sm text-muted-foreground">{performance.position}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-muted-foreground">{performance.department}</td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2">
                        <Star className={`h-4 w-4 ${getRatingColor(performance.currentRating)}`} />
                        <span className={`font-medium ${getRatingColor(performance.currentRating)}`}>
                          {performance.currentRating}
                        </span>
                        {performance.previousRating && (
                          <span className={`text-sm ${performance.currentRating > performance.previousRating ? 'text-green-600' : 'text-red-600'}`}>
                            ({performance.currentRating > performance.previousRating ? '+' : ''}{(performance.currentRating - performance.previousRating).toFixed(1)})
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-2">
                        <div className="w-20 bg-muted rounded-full h-2">
                          <div
                            className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full"
                            style={{ width: `${(performance.goalsCompleted / performance.goals) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {performance.goalsCompleted}/{performance.goals}
                        </span>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-muted-foreground">
                      {performance.reviewDate ? new Date(performance.reviewDate).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="py-4 px-6 text-muted-foreground">
                      {new Date(performance.nextReviewDate).toLocaleDateString()}
                    </td>
                    <td className="py-4 px-6">
                      <Badge variant={getStatusColor(performance.status) as any} className="flex items-center space-x-1">
                        {getStatusIcon(performance.status)}
                        <span className="ml-1">{performance.status.charAt(0).toUpperCase() + performance.status.slice(1).replace('-', ' ')}</span>
                      </Badge>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
