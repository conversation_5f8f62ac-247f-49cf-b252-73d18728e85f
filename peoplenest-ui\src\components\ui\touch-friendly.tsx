"use client"

import { useState, useRef, useEffect, ReactNode } from "react"
import { motion, PanInfo, useMotionValue, useTransform } from "framer-motion"
import { cn } from "@/lib/utils"

// Touch-friendly button with haptic feedback
interface TouchButtonProps {
  children: ReactNode
  onClick?: () => void
  variant?: "primary" | "secondary" | "ghost"
  size?: "sm" | "md" | "lg"
  disabled?: boolean
  className?: string
}

export function TouchButton({ 
  children, 
  onClick, 
  variant = "primary", 
  size = "md", 
  disabled = false,
  className 
}: TouchButtonProps) {
  const [isPressed, setIsPressed] = useState(false)

  const handleTouchStart = () => {
    setIsPressed(true)
    // Haptic feedback for supported devices
    if ('vibrate' in navigator) {
      navigator.vibrate(10)
    }
  }

  const handleTouchEnd = () => {
    setIsPressed(false)
    if (!disabled && onClick) {
      onClick()
    }
  }

  const baseClasses = "relative overflow-hidden rounded-lg font-medium transition-all duration-200 select-none"
  
  const variantClasses = {
    primary: "bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/70",
    ghost: "bg-transparent text-muted-foreground hover:bg-muted active:bg-muted/80"
  }
  
  const sizeClasses = {
    sm: "px-4 py-2 text-sm min-h-[40px]",
    md: "px-6 py-3 text-base min-h-[48px]",
    lg: "px-8 py-4 text-lg min-h-[56px]"
  }

  return (
    <motion.button
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleTouchStart}
      onMouseUp={handleTouchEnd}
      onMouseLeave={() => setIsPressed(false)}
      whileTap={{ scale: 0.98 }}
      disabled={disabled}
    >
      <motion.div
        className="absolute inset-0 bg-white/20 rounded-lg"
        initial={{ scale: 0, opacity: 0 }}
        animate={isPressed ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
        transition={{ duration: 0.2 }}
      />
      <span className="relative z-10">{children}</span>
    </motion.button>
  )
}

// Swipeable card component
interface SwipeableCardProps {
  children: ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  swipeThreshold?: number
  className?: string
}

export function SwipeableCard({ 
  children, 
  onSwipeLeft, 
  onSwipeRight, 
  swipeThreshold = 100,
  className 
}: SwipeableCardProps) {
  const x = useMotionValue(0)
  const opacity = useTransform(x, [-200, -100, 0, 100, 200], [0.5, 0.8, 1, 0.8, 0.5])
  const backgroundColor = useTransform(
    x,
    [-200, -100, 0, 100, 200],
    ["#ef4444", "#f87171", "#ffffff", "#10b981", "#059669"]
  )

  const handleDragEnd = (event: any, info: PanInfo) => {
    const offset = info.offset.x
    
    if (offset > swipeThreshold && onSwipeRight) {
      onSwipeRight()
    } else if (offset < -swipeThreshold && onSwipeLeft) {
      onSwipeLeft()
    }
    
    // Reset position
    x.set(0)
  }

  return (
    <motion.div
      className={cn("relative bg-white rounded-lg shadow-sm border border-gray-200", className)}
      style={{ x, opacity, backgroundColor }}
      drag="x"
      dragConstraints={{ left: -200, right: 200 }}
      dragElastic={0.2}
      onDragEnd={handleDragEnd}
      whileDrag={{ scale: 1.02 }}
    >
      {children}
      
      {/* Swipe indicators */}
      <motion.div
        className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white font-medium"
        style={{ opacity: useTransform(x, [-200, -100, 0], [1, 0.5, 0]) }}
      >
        Delete
      </motion.div>
      
      <motion.div
        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white font-medium"
        style={{ opacity: useTransform(x, [0, 100, 200], [0, 0.5, 1]) }}
      >
        Archive
      </motion.div>
    </motion.div>
  )
}

// Pull-to-refresh component
interface PullToRefreshProps {
  children: ReactNode
  onRefresh: () => Promise<void>
  refreshThreshold?: number
  className?: string
}

export function PullToRefresh({ 
  children, 
  onRefresh, 
  refreshThreshold = 80,
  className 
}: PullToRefreshProps) {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleTouchStart = (e: React.TouchEvent) => {
    const container = containerRef.current
    if (container && container.scrollTop === 0) {
      const touch = e.touches[0]
      container.dataset.startY = touch.clientY.toString()
    }
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    const container = containerRef.current
    if (container && container.dataset.startY && container.scrollTop === 0) {
      const touch = e.touches[0]
      const startY = parseInt(container.dataset.startY)
      const currentY = touch.clientY
      const distance = Math.max(0, currentY - startY)
      
      if (distance > 0) {
        e.preventDefault()
        setPullDistance(Math.min(distance, refreshThreshold * 1.5))
      }
    }
  }

  const handleTouchEnd = async () => {
    const container = containerRef.current
    if (container) {
      delete container.dataset.startY
      
      if (pullDistance >= refreshThreshold && !isRefreshing) {
        setIsRefreshing(true)
        try {
          await onRefresh()
        } finally {
          setIsRefreshing(false)
        }
      }
      
      setPullDistance(0)
    }
  }

  const refreshProgress = Math.min(pullDistance / refreshThreshold, 1)

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-auto", className)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull indicator */}
      <motion.div
        className="absolute top-0 left-0 right-0 flex items-center justify-center bg-primary/10 text-primary"
        style={{ height: pullDistance }}
        initial={{ opacity: 0 }}
        animate={{ opacity: pullDistance > 0 ? 1 : 0 }}
      >
        <motion.div
          className="flex items-center space-x-2"
          animate={{ 
            rotate: isRefreshing ? 360 : refreshProgress * 180 
          }}
          transition={{ 
            duration: isRefreshing ? 1 : 0,
            repeat: isRefreshing ? Infinity : 0,
            ease: "linear"
          }}
        >
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full" />
        </motion.div>
        <span className="ml-2 text-sm font-medium">
          {isRefreshing ? "Refreshing..." : pullDistance >= refreshThreshold ? "Release to refresh" : "Pull to refresh"}
        </span>
      </motion.div>
      
      <motion.div
        style={{ paddingTop: pullDistance }}
        transition={{ type: "spring", damping: 20, stiffness: 300 }}
      >
        {children}
      </motion.div>
    </div>
  )
}

// Touch-friendly input with better mobile UX
interface TouchInputProps {
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  type?: "text" | "email" | "password" | "number" | "tel"
  autoComplete?: string
  className?: string
}

export function TouchInput({ 
  placeholder, 
  value, 
  onChange, 
  type = "text",
  autoComplete,
  className 
}: TouchInputProps) {
  const [isFocused, setIsFocused] = useState(false)

  return (
    <motion.div
      className={cn(
        "relative border border-gray-200 rounded-lg transition-all duration-200",
        isFocused && "border-blue-500 ring-2 ring-blue-100",
        className
      )}
      whileTap={{ scale: 0.99 }}
    >
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        autoComplete={autoComplete}
        className="w-full px-4 py-3 text-base bg-transparent rounded-lg outline-none min-h-[48px]"
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        // Prevent zoom on iOS
        style={{ fontSize: '16px' }}
      />
    </motion.div>
  )
}

// Floating Action Button
interface FABProps {
  icon: ReactNode
  onClick: () => void
  position?: "bottom-right" | "bottom-left" | "bottom-center"
  className?: string
}

export function FloatingActionButton({ 
  icon, 
  onClick, 
  position = "bottom-right",
  className 
}: FABProps) {
  const positionClasses = {
    "bottom-right": "bottom-6 right-6",
    "bottom-left": "bottom-6 left-6",
    "bottom-center": "bottom-6 left-1/2 transform -translate-x-1/2"
  }

  return (
    <motion.button
      className={cn(
        "fixed z-50 w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg flex items-center justify-center",
        positionClasses[position],
        className
      )}
      onClick={onClick}
      whileTap={{ scale: 0.9 }}
      whileHover={{ scale: 1.1 }}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", damping: 15, stiffness: 300 }}
    >
      {icon}
    </motion.button>
  )
}
