import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'

export class PerformanceService {
  private db: DatabaseService

  constructor() {
    this.db = new DatabaseService()
  }

  async getPerformanceReviews(employeeId: string, user: any): Promise<any> {
    try {
      const query = `
        SELECT * FROM performance_reviews 
        WHERE employee_id = $1
        ORDER BY review_period DESC
      `
      const result = await this.db.query(query, [employeeId])
      return result.rows
    } catch (error) {
      logger.error('Error fetching performance reviews:', error)
      throw error
    }
  }

  async createPerformanceReview(data: any, user: any): Promise<any> {
    try {
      const query = `
        INSERT INTO performance_reviews (employee_id, reviewer_id, review_period, goals, achievements, rating)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *
      `
      const result = await this.db.query(query, [
        data.employeeId,
        user.id,
        data.reviewPeriod,
        data.goals,
        data.achievements,
        data.rating
      ])
      return result.rows[0]
    } catch (error) {
      logger.error('Error creating performance review:', error)
      throw error
    }
  }

  async updatePerformanceReview(id: string, data: any, user: any): Promise<any> {
    try {
      const query = `
        UPDATE performance_reviews 
        SET goals = $1, achievements = $2, rating = $3, updated_at = NOW()
        WHERE id = $4
        RETURNING *
      `
      const result = await this.db.query(query, [
        data.goals,
        data.achievements,
        data.rating,
        id
      ])
      return result.rows[0]
    } catch (error) {
      logger.error('Error updating performance review:', error)
      throw error
    }
  }

  async getPerformanceGoals(employeeId: string, user: any): Promise<any> {
    try {
      const query = `
        SELECT * FROM performance_goals 
        WHERE employee_id = $1
        ORDER BY target_date ASC
      `
      const result = await this.db.query(query, [employeeId])
      return result.rows
    } catch (error) {
      logger.error('Error fetching performance goals:', error)
      throw error
    }
  }

  async createPerformanceGoal(data: any, user: any): Promise<any> {
    try {
      const query = `
        INSERT INTO performance_goals (employee_id, title, description, target_date, created_by)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `
      const result = await this.db.query(query, [
        data.employeeId,
        data.title,
        data.description,
        data.targetDate,
        user.id
      ])
      return result.rows[0]
    } catch (error) {
      logger.error('Error creating performance goal:', error)
      throw error
    }
  }
}
