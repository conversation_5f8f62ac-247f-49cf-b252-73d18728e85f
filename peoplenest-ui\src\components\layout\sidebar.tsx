"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
  Building2,
  LayoutDashboard,
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  FileText,
  Settings,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
  Bell,
  Search,
  LogOut,
  User,
  Shield,
  BarChart3,
  Clock,
  Award,
  MessageSquare
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface SidebarProps {
  className?: string
}

const navigationItems = [
  {
    title: "Overview",
    items: [
      { name: "Dashboard", icon: LayoutDashboard, href: "/dashboard", badge: null },
      { name: "Analytics", icon: BarChart3, href: "/dashboard", badge: "Coming Soon" },
    ]
  },
  {
    title: "People Management",
    items: [
      { name: "Employees", icon: Users, href: "/dashboard/employees", badge: "124" },
      { name: "Recruitment", icon: User, href: "/dashboard/employees", badge: "Coming Soon" },
      { name: "Onboarding", icon: Award, href: "/dashboard/employees", badge: "Coming Soon" },
    ]
  },
  {
    title: "Operations",
    items: [
      { name: "Payroll", icon: DollarSign, href: "/dashboard/payroll", badge: null },
      { name: "Performance", icon: TrendingUp, href: "/dashboard/performance", badge: "5" },
      { name: "Time & Attendance", icon: Clock, href: "/dashboard", badge: "Coming Soon" },
      { name: "Leave Management", icon: Calendar, href: "/dashboard", badge: "Coming Soon" },
    ]
  },
  {
    title: "Communication",
    items: [
      { name: "Announcements", icon: MessageSquare, href: "/dashboard", badge: "Coming Soon" },
      { name: "Documents", icon: FileText, href: "/dashboard", badge: "Coming Soon" },
    ]
  },
  {
    title: "System",
    items: [
      { name: "Settings", icon: Settings, href: "/dashboard", badge: "Coming Soon" },
      { name: "Compliance", icon: Shield, href: "/dashboard", badge: "Coming Soon" },
      { name: "Help & Support", icon: HelpCircle, href: "/dashboard", badge: "Coming Soon" },
    ]
  }
]

export function Sidebar({ className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [activeItem, setActiveItem] = useState("/dashboard")

  return (
    <motion.div
      initial={false}
      animate={{ width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className={cn(
        "relative flex flex-col bg-white border-r border-gray-200 shadow-sm",
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <AnimatePresence mode="wait">
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
              className="flex items-center space-x-3"
            >
              <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg">
                <Building2 className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-foreground">PeopleNest</h1>
                <p className="text-xs text-muted-foreground">HRMS Platform</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 text-muted-foreground hover:text-foreground"
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto py-4">
        <nav className="space-y-6 px-3">
          {navigationItems.map((section, sectionIndex) => (
            <div key={section.title}>
              <AnimatePresence>
                {!isCollapsed && (
                  <motion.h3
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3"
                  >
                    {section.title}
                  </motion.h3>
                )}
              </AnimatePresence>
              
              <ul className="space-y-1">
                {section.items.map((item) => {
                  const isActive = activeItem === item.href
                  return (
                    <li key={item.name}>
                      <button
                        onClick={() => setActiveItem(item.href)}
                        className={cn(
                          "w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",
                          isActive
                            ? "bg-primary text-primary-foreground shadow-sm"
                            : "text-foreground hover:bg-muted hover:text-foreground"
                        )}
                      >
                        <item.icon className={cn(
                          "flex-shrink-0 w-5 h-5",
                          isCollapsed ? "mx-auto" : "mr-3"
                        )} />
                        
                        <AnimatePresence>
                          {!isCollapsed && (
                            <motion.div
                              initial={{ opacity: 0, width: 0 }}
                              animate={{ opacity: 1, width: "auto" }}
                              exit={{ opacity: 0, width: 0 }}
                              className="flex items-center justify-between flex-1 min-w-0"
                            >
                              <span className="truncate">{item.name}</span>
                              {item.badge && (
                                <Badge
                                  variant={isActive ? "secondary" : "outline"}
                                  className="ml-2 text-xs"
                                >
                                  {item.badge}
                                </Badge>
                              )}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </button>
                    </li>
                  )
                })}
              </ul>
            </div>
          ))}
        </nav>
      </div>

      {/* User Profile */}
      <div className="border-t border-gray-200 p-4">
        <div className={cn(
          "flex items-center",
          isCollapsed ? "justify-center" : "space-x-3"
        )}>
          <Avatar className="h-8 w-8">
            <AvatarImage src="/avatars/user.jpg" alt="User" />
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
          
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: "auto" }}
                exit={{ opacity: 0, width: 0 }}
                className="flex-1 min-w-0"
              >
                <p className="text-sm font-medium text-foreground truncate">
                  John Doe
                </p>
                <p className="text-xs text-muted-foreground truncate">
                  HR Manager
                </p>
              </motion.div>
            )}
          </AnimatePresence>
          
          <AnimatePresence>
            {!isCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <LogOut className="h-4 w-4" />
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  )
}
