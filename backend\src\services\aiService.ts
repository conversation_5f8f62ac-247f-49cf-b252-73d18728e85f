import { DatabaseService } from './databaseService'
import { AIProviderService } from './aiProviderService'
import { logger } from '../utils/logger'
import { v4 as uuidv4 } from 'uuid'

interface ResumeParseOptions {
  jobPositionId?: string
  candidateName?: string
  extractSkills: boolean
  extractExperience: boolean
  extractEducation: boolean
  extractContact: boolean
}

interface ParsedResume {
  id: string
  candidateName?: string
  contact: {
    email?: string
    phone?: string
    address?: string
    linkedin?: string
  }
  summary?: string
  skills: Array<{
    name: string
    category: string
    level?: string
  }>
  experience: Array<{
    company: string
    position: string
    startDate?: string
    endDate?: string
    description: string
    skills: string[]
  }>
  education: Array<{
    institution: string
    degree: string
    field: string
    startDate?: string
    endDate?: string
    gpa?: string
  }>
  certifications: Array<{
    name: string
    issuer: string
    issueDate?: string
    expiryDate?: string
  }>
  languages: Array<{
    name: string
    proficiency: string
  }>
  metadata: {
    fileName: string
    fileSize: number
    parseDate: string
    confidence: number
  }
}

interface JobMatchResult {
  matchId: string
  overallScore: number
  skillsMatch: {
    score: number
    matchedSkills: string[]
    missingSkills: string[]
    additionalSkills: string[]
  }
  experienceMatch: {
    score: number
    yearsRequired: number
    yearsCandidate: number
    relevantExperience: Array<{
      company: string
      position: string
      relevanceScore: number
    }>
  }
  educationMatch: {
    score: number
    required: string
    candidate: string[]
    meetsRequirement: boolean
  }
  recommendations: string[]
  strengths: string[]
  concerns: string[]
}

interface SentimentAnalysisResult {
  id: string
  sentiment: 'positive' | 'negative' | 'neutral'
  confidence: number
  score: number // -1 to 1
  emotions: Array<{
    emotion: string
    intensity: number
  }>
  keywords: Array<{
    word: string
    sentiment: string
    weight: number
  }>
  summary: string
  recommendations?: string[]
}

interface AttritionPrediction {
  employeeId: string
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  probability: number
  timeframe: string
  factors: Array<{
    factor: string
    impact: number
    description: string
  }>
  recommendations: string[]
  lastUpdated: string
}

interface PerformanceInsights {
  employeeId: string
  overallScore: number
  trends: {
    direction: 'improving' | 'declining' | 'stable'
    rate: number
    period: string
  }
  strengths: Array<{
    area: string
    score: number
    evidence: string[]
  }>
  improvementAreas: Array<{
    area: string
    priority: 'high' | 'medium' | 'low'
    suggestions: string[]
  }>
  goalProgress: Array<{
    goalId: string
    title: string
    progress: number
    status: string
    insights: string[]
  }>
  peerComparison: {
    percentile: number
    department: string
    role: string
  }
  predictions: {
    nextQuarterScore: number
    confidence: number
    factors: string[]
  }
}

interface CareerRecommendations {
  employeeId: string
  currentRole: string
  careerPath: Array<{
    role: string
    timeframe: string
    probability: number
    requirements: {
      skills: string[]
      experience: string[]
      education: string[]
    }
    steps: Array<{
      action: string
      priority: number
      timeline: string
      resources: string[]
    }>
  }>
  skillDevelopment: Array<{
    skill: string
    currentLevel: string
    targetLevel: string
    importance: number
    learningResources: Array<{
      type: string
      title: string
      provider: string
      duration: string
      cost?: number
    }>
  }>
  mentorship: {
    recommended: boolean
    mentorProfiles: Array<{
      name: string
      role: string
      expertise: string[]
      matchScore: number
    }>
  }
  networking: {
    internalConnections: string[]
    externalOpportunities: string[]
    events: string[]
  }
}

interface SkillsGapAnalysis {
  targetRole?: string
  currentSkills: Array<{
    name: string
    level: string
    verified: boolean
  }>
  requiredSkills: Array<{
    name: string
    level: string
    importance: 'critical' | 'important' | 'nice_to_have'
  }>
  gaps: Array<{
    skill: string
    currentLevel: string
    requiredLevel: string
    priority: number
    trainingRecommendations: Array<{
      type: string
      provider: string
      duration: string
      cost?: number
      effectiveness: number
    }>
  }>
  strengths: string[]
  readinessScore: number
  estimatedTimeToReady: string
}

export class AIService {
  private db: DatabaseService
  private aiProvider: AIProviderService

  constructor(db?: DatabaseService, aiProvider?: AIProviderService) {
    this.db = db || new DatabaseService()
    this.aiProvider = aiProvider || new AIProviderService()
  }

  /**
   * Parse resume using AI
   */
  async parseResume(
    file: Express.Multer.File,
    options: ResumeParseOptions,
    userId: string
  ): Promise<ParsedResume> {
    try {
      // Validate file type
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      if (!allowedTypes.includes(file.mimetype)) {
        throw new Error('Unsupported file type')
      }

      // Validate file size (max 10MB)
      const maxSize = 10 * 1024 * 1024
      if (file.size > maxSize) {
        throw new Error('File too large')
      }

      const requestId = uuidv4()

      // Log the request
      await this.logAIRequest('resume_parsing', requestId, userId, {
        fileName: file.originalname,
        fileSize: file.size,
        options
      })

      // Try AI parsing first, fallback to mock if not available
      const parsedResume = await this.parseResumeWithAI(file, options)
      
      // Store parsed resume in database
      await this.db.query(`
        INSERT INTO parsed_resumes (id, user_id, file_name, file_size, parsed_data, created_at)
        VALUES ($1, $2, $3, $4, $5, NOW())
      `, [parsedResume.id, userId, file.originalname, file.size, JSON.stringify(parsedResume)])

      // Log successful completion
      await this.logAIResponse('resume_parsing', requestId, userId, 'success', {
        resumeId: parsedResume.id,
        confidence: parsedResume.metadata.confidence
      })

      return parsedResume
    } catch (error) {
      logger.error('Resume parsing error:', error)
      // Re-throw validation errors with their original message
      if (error instanceof Error && (
        error.message.includes('Unsupported file type') ||
        error.message.includes('File too large')
      )) {
        throw error
      }
      throw new Error('Failed to parse resume')
    }
  }

  /**
   * Match resume against job requirements
   */
  async matchResumeToJob(
    resumeData: ParsedResume,
    jobRequirements: any,
    weightings: any = {},
    userId: string
  ): Promise<JobMatchResult> {
    try {
      // Validate resume exists
      const resumeCheck = await this.db.query(
        'SELECT id FROM parsed_resumes WHERE id = $1',
        [resumeData.id]
      )
      if (resumeCheck.rowCount === 0) {
        throw new Error('Resume not found')
      }

      // Validate job exists (simplified check)
      if (!jobRequirements || !jobRequirements.id) {
        throw new Error('Job not found')
      }

      const requestId = uuidv4()

      await this.logAIRequest('resume_matching', requestId, userId, {
        resumeId: resumeData.id,
        jobRequirements
      })

      // Mock matching logic - In production, this would use AI
      const matchResult = await this.mockResumeMatching(resumeData, jobRequirements, weightings)

      await this.logAIResponse('resume_matching', requestId, userId, 'success', {
        overallScore: matchResult.overallScore
      })

      return matchResult
    } catch (error) {
      logger.error('Resume matching error:', error)
      // Re-throw validation errors with their original message
      if (error instanceof Error && (
        error.message.includes('Resume not found') ||
        error.message.includes('Job not found')
      )) {
        throw error
      }
      throw new Error('Failed to match resume to job')
    }
  }

  /**
   * Analyze sentiment of text
   */
  async analyzeSentiment(
    text: string,
    context: any,
    userId: string
  ): Promise<SentimentAnalysisResult> {
    try {
      // Validate text input
      if (!text || text.trim().length === 0) {
        throw new Error('Text is required for sentiment analysis')
      }

      // Validate text length (max 5000 characters)
      if (text.length > 5000) {
        throw new Error('Text too long for sentiment analysis')
      }

      const requestId = uuidv4()

      await this.logAIRequest('sentiment_analysis', requestId, userId, {
        textLength: text.length,
        context
      })

      // Try AI sentiment analysis first, fallback to mock if not available
      const sentimentResult = await this.analyzeSentimentWithAI(text, context)
      
      await this.logAIResponse('sentiment_analysis', requestId, userId, 'success', {
        sentiment: sentimentResult.sentiment,
        confidence: sentimentResult.confidence
      })

      return sentimentResult
    } catch (error) {
      logger.error('Sentiment analysis error:', error)
      // Re-throw validation errors with their original message
      if (error instanceof Error && (
        error.message.includes('Text is required for sentiment analysis') ||
        error.message.includes('Text too long for sentiment analysis')
      )) {
        throw error
      }
      throw new Error('Failed to analyze sentiment')
    }
  }

  /**
   * Predict employee attrition risk
   */
  async predictAttrition(
    options: any,
    userId: string
  ): Promise<AttritionPrediction[]> {
    try {
      // Validate timeframe
      const validTimeframes = ['3_months', '6_months', '12_months']
      if (options.timeframe && !validTimeframes.includes(options.timeframe)) {
        throw new Error('Invalid timeframe')
      }

      const requestId = uuidv4()

      await this.logAIRequest('attrition_prediction', requestId, userId, options)

      // Mock attrition prediction - In production, this would use ML models
      const predictions = await this.mockAttritionPrediction(options)

      await this.logAIResponse('attrition_prediction', requestId, userId, 'success', {
        predictionsCount: predictions.length
      })

      return predictions
    } catch (error) {
      logger.error('Attrition prediction error:', error)
      // Re-throw validation errors with their original message
      if (error instanceof Error && error.message.includes('Invalid timeframe')) {
        throw error
      }
      throw new Error('Failed to predict attrition')
    }
  }

  /**
   * Generate performance insights
   */
  async generatePerformanceInsights(
    employeeId: string,
    options: any,
    userId: string
  ): Promise<PerformanceInsights> {
    try {
      // Validate employee exists
      const employeeCheck = await this.db.query(
        'SELECT id FROM employees WHERE id = $1',
        [employeeId]
      )
      if (employeeCheck.rowCount === 0) {
        throw new Error('Employee not found')
      }

      const requestId = uuidv4()

      await this.logAIRequest('performance_insights', requestId, userId, {
        employeeId,
        options
      })

      // Mock performance insights - In production, this would use AI
      const insights = await this.mockPerformanceInsights(employeeId, options)

      await this.logAIResponse('performance_insights', requestId, userId, 'success', {
        employeeId,
        overallScore: insights.overallScore
      })

      return insights
    } catch (error) {
      logger.error('Performance insights error:', error)
      // Re-throw validation errors with their original message
      if (error instanceof Error && error.message.includes('Employee not found')) {
        throw error
      }
      throw new Error('Failed to generate performance insights')
    }
  }

  /**
   * Generate career recommendations
   */
  async generateCareerRecommendations(
    employeeId: string,
    options: any,
    userId: string
  ): Promise<CareerRecommendations> {
    try {
      const requestId = uuidv4()

      await this.logAIRequest('career_recommendations', requestId, userId, {
        employeeId,
        options
      })

      // Mock career recommendations - In production, this would use AI
      const recommendations = await this.mockCareerRecommendations(employeeId, options)

      await this.logAIResponse('career_recommendations', requestId, userId, 'success', {
        employeeId,
        pathsCount: recommendations.careerPath.length
      })

      return recommendations
    } catch (error) {
      logger.error('Career recommendations error:', error)
      throw new Error('Failed to generate career recommendations')
    }
  }

  /**
   * Perform skills gap analysis
   */
  async performSkillsGapAnalysis(
    options: any,
    userId: string
  ): Promise<SkillsGapAnalysis> {
    try {
      const requestId = uuidv4()

      await this.logAIRequest('skills_gap_analysis', requestId, userId, options)

      // Mock skills gap analysis - In production, this would use AI
      const analysis = await this.mockSkillsGapAnalysis(options)

      await this.logAIResponse('skills_gap_analysis', requestId, userId, 'success', {
        readinessScore: analysis.readinessScore,
        gapsCount: analysis.gaps.length
      })

      return analysis
    } catch (error) {
      logger.error('Skills gap analysis error:', error)
      throw new Error('Failed to perform skills gap analysis')
    }
  }

  /**
   * Get AI service usage analytics
   */
  async getUsageAnalytics(filters: any): Promise<any> {
    try {
      const query = `
        SELECT
          service_type,
          COUNT(*) as total_requests,
          COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_requests,
          COUNT(CASE WHEN status = 'error' THEN 1 END) as failed_requests,
          AVG(EXTRACT(EPOCH FROM (response_time - request_time))) as avg_response_time,
          DATE_TRUNC('day', request_time) as date
        FROM ai_service_logs
        WHERE request_time >= COALESCE($1, NOW() - INTERVAL '30 days')
          AND request_time <= COALESCE($2, NOW())
          AND ($3 IS NULL OR service_type = $3)
        GROUP BY service_type, DATE_TRUNC('day', request_time)
        ORDER BY date DESC, service_type
      `

      const result = await this.db.query(query, [
        filters.startDate,
        filters.endDate,
        filters.service
      ])

      return {
        analytics: result.rows,
        summary: {
          totalRequests: result.rows.reduce((sum, row) => sum + parseInt(row.total_requests), 0),
          successRate: result.rows.length > 0
            ? result.rows.reduce((sum, row) => sum + parseInt(row.successful_requests), 0) /
              result.rows.reduce((sum, row) => sum + parseInt(row.total_requests), 0) * 100
            : 0,
          avgResponseTime: result.rows.length > 0
            ? result.rows.reduce((sum, row) => sum + parseFloat(row.avg_response_time), 0) / result.rows.length
            : 0
        }
      }
    } catch (error) {
      logger.error('Error fetching AI usage analytics:', error)
      throw new Error('Failed to fetch AI usage analytics')
    }
  }

  /**
   * Submit training feedback
   */
  async submitTrainingFeedback(
    feedback: any,
    userId: string
  ): Promise<any> {
    try {
      const feedbackId = uuidv4()

      await this.db.query(`
        INSERT INTO ai_training_feedback (
          id, service_type, request_id, user_id, rating, feedback,
          corrections, helpful, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
      `, [
        feedbackId,
        feedback.service,
        feedback.requestId,
        userId,
        feedback.rating,
        feedback.feedback,
        JSON.stringify(feedback.corrections),
        feedback.helpful
      ])

      logger.info('AI training feedback submitted', {
        feedbackId,
        service: feedback.service,
        rating: feedback.rating,
        userId
      })

      return {
        id: feedbackId,
        message: 'Feedback submitted successfully'
      }
    } catch (error) {
      logger.error('Error submitting AI training feedback:', error)
      throw new Error('Failed to submit training feedback')
    }
  }

  // Helper methods for logging
  private async logAIRequest(
    _serviceType: string,
    requestId: string,
    _userId: string,
    requestData: any
  ): Promise<void> {
    try {
      await this.db.query(`
        INSERT INTO ai_service_logs (
          id, service_type, user_id, request_time, request_data, status
        ) VALUES ($1, $2, $3, NOW(), $4, 'pending')
      `, [requestId, _serviceType, _userId, JSON.stringify(requestData)])
    } catch (error) {
      logger.error('Error logging AI request:', error)
    }
  }

  private async logAIResponse(
    _serviceType: string,
    requestId: string,
    _userId: string,
    status: string,
    responseData: any
  ): Promise<void> {
    try {
      await this.db.query(`
        UPDATE ai_service_logs
        SET response_time = NOW(), status = $1, response_data = $2
        WHERE id = $3
      `, [status, JSON.stringify(responseData), requestId])
    } catch (error) {
      logger.error('Error logging AI response:', error)
    }
  }

  // Mock implementations - Replace with actual AI service calls in production
  private async mockResumeParser(
    file: Express.Multer.File,
    options: ResumeParseOptions
  ): Promise<ParsedResume> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000))

    return {
      id: uuidv4(),
      candidateName: options.candidateName || 'John Doe',
      contact: {
        email: '<EMAIL>',
        phone: '******-0123',
        address: '123 Main St, City, State 12345',
        linkedin: 'linkedin.com/in/johndoe'
      },
      summary: 'Experienced software developer with 5+ years in full-stack development.',
      skills: [
        { name: 'JavaScript', category: 'Programming', level: 'advanced' },
        { name: 'React', category: 'Frontend', level: 'advanced' },
        { name: 'Node.js', category: 'Backend', level: 'intermediate' },
        { name: 'PostgreSQL', category: 'Database', level: 'intermediate' }
      ],
      experience: [
        {
          company: 'Tech Corp',
          position: 'Senior Developer',
          startDate: '2020-01',
          endDate: '2023-12',
          description: 'Led development of web applications using React and Node.js',
          skills: ['React', 'Node.js', 'PostgreSQL']
        }
      ],
      education: [
        {
          institution: 'University of Technology',
          degree: 'Bachelor of Science',
          field: 'Computer Science',
          startDate: '2015-09',
          endDate: '2019-05',
          gpa: '3.8'
        }
      ],
      certifications: [
        {
          name: 'AWS Certified Developer',
          issuer: 'Amazon Web Services',
          issueDate: '2022-03',
          expiryDate: '2025-03'
        }
      ],
      languages: [
        { name: 'English', proficiency: 'native' },
        { name: 'Spanish', proficiency: 'intermediate' }
      ],
      metadata: {
        fileName: file.originalname,
        fileSize: file.size,
        parseDate: new Date().toISOString(),
        confidence: 0.92
      }
    }
  }

  private async mockResumeMatching(
    resumeData: ParsedResume,
    jobRequirements: any,
    _weightings: any
  ): Promise<JobMatchResult> {
    await new Promise(resolve => setTimeout(resolve, 500))

    const candidateSkills = resumeData.skills.map(s => s.name.toLowerCase())
    const requiredSkills = jobRequirements.skills.map((s: string) => s.toLowerCase())

    const matchedSkills = candidateSkills.filter(skill =>
      requiredSkills.includes(skill)
    )
    const missingSkills = requiredSkills.filter((skill: string) =>
      !candidateSkills.includes(skill)
    )

    const skillsScore = requiredSkills.length > 0 ? (matchedSkills.length / requiredSkills.length * 100) : 100

    // Parse experience requirement (handle strings like "2-5 years")
    let requiredYears = 3 // default
    if (typeof jobRequirements.experience === 'number') {
      requiredYears = jobRequirements.experience
    } else if (typeof jobRequirements.experience === 'string') {
      const match = jobRequirements.experience.match(/(\d+)/)
      if (match) {
        requiredYears = parseInt(match[1])
      }
    }

    const candidateYears = resumeData.experience.length * 2
    const experienceScore = Math.min((candidateYears / requiredYears) * 100, 100)
    const educationScore = resumeData.education.length > 0 ? 85 : 60

    const overallScore = (skillsScore * 0.5) + (experienceScore * 0.3) + (educationScore * 0.2)

    return {
      matchId: uuidv4(),
      overallScore: Math.round(overallScore),
      skillsMatch: {
        score: Math.round(skillsScore),
        matchedSkills: matchedSkills,
        missingSkills: missingSkills,
        additionalSkills: candidateSkills.filter(skill => !requiredSkills.includes(skill))
      },
      experienceMatch: {
        score: Math.round(experienceScore),
        yearsRequired: requiredYears,
        yearsCandidate: candidateYears,
        relevantExperience: resumeData.experience.map(exp => ({
          company: exp.company,
          position: exp.position,
          relevanceScore: Math.random() * 40 + 60
        }))
      },
      educationMatch: {
        score: Math.round(educationScore),
        required: jobRequirements.education || 'Bachelor\'s degree',
        candidate: resumeData.education.map(edu => `${edu.degree} in ${edu.field}`),
        meetsRequirement: resumeData.education.length > 0
      },
      recommendations: [
        'Strong technical skills match',
        'Consider additional training in missing skills',
        'Good cultural fit based on experience'
      ],
      strengths: [
        'Relevant technical experience',
        'Strong educational background',
        'Diverse skill set'
      ],
      concerns: [
        'Missing some required skills',
        'May need additional training'
      ]
    }
  }

  private async mockSentimentAnalysis(
    text: string,
    _context: any
  ): Promise<SentimentAnalysisResult> {
    await new Promise(resolve => setTimeout(resolve, 300))

    // Simple mock sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'love', 'happy', 'satisfied']
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'disappointed', 'frustrated', 'angry']

    const words = text.toLowerCase().split(/\s+/)
    const positiveCount = words.filter(word => positiveWords.includes(word)).length
    const negativeCount = words.filter(word => negativeWords.includes(word)).length

    let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral'
    let score = 0

    if (positiveCount > negativeCount) {
      sentiment = 'positive'
      score = Math.min(0.8, (positiveCount - negativeCount) / words.length * 10)
    } else if (negativeCount > positiveCount) {
      sentiment = 'negative'
      score = Math.max(-0.8, -(negativeCount - positiveCount) / words.length * 10)
    }

    return {
      id: uuidv4(),
      sentiment,
      confidence: Math.random() * 0.3 + 0.7,
      score,
      emotions: [
        { emotion: 'joy', intensity: sentiment === 'positive' ? 0.7 : 0.2 },
        { emotion: 'anger', intensity: sentiment === 'negative' ? 0.6 : 0.1 },
        { emotion: 'neutral', intensity: sentiment === 'neutral' ? 0.8 : 0.3 }
      ],
      keywords: words.slice(0, 10).map(word => ({
        word,
        sentiment: positiveWords.includes(word) ? 'positive' :
                  negativeWords.includes(word) ? 'negative' : 'neutral',
        weight: Math.random()
      })),
      summary: `The text expresses ${sentiment} sentiment.`,
      recommendations: sentiment === 'negative' ? [
        'Consider addressing the concerns raised',
        'Follow up with additional support'
      ] : []
    }
  }

  private async mockAttritionPrediction(options: any): Promise<AttritionPrediction[]> {
    await new Promise(resolve => setTimeout(resolve, 800))

    // Mock predictions for demonstration
    const mockEmployees = ['emp1', 'emp2', 'emp3', 'emp4', 'emp5']
    const riskLevels: Array<'low' | 'medium' | 'high' | 'critical'> = ['low', 'medium', 'high', 'critical']

    return mockEmployees.map(empId => ({
      employeeId: empId,
      riskLevel: riskLevels[Math.floor(Math.random() * riskLevels.length)],
      probability: Math.random(),
      timeframe: options.timeframe || '6_months',
      factors: [
        { factor: 'Job satisfaction', impact: Math.random() * 0.4 + 0.1, description: 'Recent survey scores' },
        { factor: 'Workload', impact: Math.random() * 0.3 + 0.1, description: 'Hours worked per week' },
        { factor: 'Career growth', impact: Math.random() * 0.3 + 0.1, description: 'Promotion opportunities' }
      ],
      recommendations: [
        'Schedule one-on-one meeting',
        'Review compensation package',
        'Discuss career development opportunities'
      ],
      lastUpdated: new Date().toISOString()
    }))
  }

  private async mockPerformanceInsights(
    employeeId: string,
    _options: any
  ): Promise<PerformanceInsights> {
    await new Promise(resolve => setTimeout(resolve, 600))

    const overallScore = Math.random() * 40 + 60 // 60-100 range
    const trends = ['improving', 'declining', 'stable'] as const
    const trend = trends[Math.floor(Math.random() * trends.length)]

    return {
      employeeId,
      overallScore: Math.round(overallScore),
      trends: {
        direction: trend,
        rate: Math.random() * 10 + 1,
        period: _options.timeframe || 'quarter'
      },
      strengths: [
        { area: 'Technical Skills', score: 85, evidence: ['Completed advanced training', 'Mentored junior developers'] },
        { area: 'Communication', score: 78, evidence: ['Positive peer feedback', 'Clear documentation'] },
        { area: 'Problem Solving', score: 92, evidence: ['Resolved critical issues', 'Innovative solutions'] }
      ],
      improvementAreas: [
        { area: 'Time Management', priority: 'medium', suggestions: ['Use project management tools', 'Set daily priorities'] },
        { area: 'Leadership', priority: 'low', suggestions: ['Take on team lead role', 'Attend leadership training'] }
      ],
      goalProgress: [
        { goalId: 'goal1', title: 'Complete certification', progress: 75, status: 'on_track', insights: ['Making good progress', 'On schedule'] },
        { goalId: 'goal2', title: 'Improve code quality', progress: 90, status: 'ahead', insights: ['Excellent improvement', 'Exceeding expectations'] }
      ],
      peerComparison: {
        percentile: Math.round(Math.random() * 40 + 50), // 50-90 percentile
        department: 'Engineering',
        role: 'Software Developer'
      },
      predictions: {
        nextQuarterScore: Math.round(overallScore + (Math.random() * 10 - 5)),
        confidence: Math.random() * 0.3 + 0.7,
        factors: ['Current trajectory', 'Goal completion rate', 'Peer feedback']
      }
    }
  }

  private async mockCareerRecommendations(
    employeeId: string,
    _options: any
  ): Promise<CareerRecommendations> {
    await new Promise(resolve => setTimeout(resolve, 700))

    return {
      employeeId,
      currentRole: 'Software Developer',
      careerPath: [
        {
          role: 'Senior Software Developer',
          timeframe: '6-12 months',
          probability: 0.85,
          requirements: {
            skills: ['Advanced JavaScript', 'System Design', 'Mentoring'],
            experience: ['Lead a project', '2+ years current role'],
            education: ['Optional: Advanced certification']
          },
          steps: [
            { action: 'Lead next major project', priority: 1, timeline: '3 months', resources: ['Project management training'] },
            { action: 'Mentor junior developer', priority: 2, timeline: '2 months', resources: ['Mentoring guidelines'] },
            { action: 'Complete system design course', priority: 3, timeline: '4 months', resources: ['Online courses', 'Books'] }
          ]
        },
        {
          role: 'Tech Lead',
          timeframe: '18-24 months',
          probability: 0.65,
          requirements: {
            skills: ['Leadership', 'Architecture', 'Team Management'],
            experience: ['Senior role', 'Cross-team collaboration'],
            education: ['Leadership training']
          },
          steps: [
            { action: 'Take leadership training', priority: 1, timeline: '6 months', resources: ['Internal training', 'External courses'] },
            { action: 'Lead cross-functional project', priority: 2, timeline: '8 months', resources: ['Project opportunities'] }
          ]
        }
      ],
      skillDevelopment: [
        {
          skill: 'System Design',
          currentLevel: 'intermediate',
          targetLevel: 'advanced',
          importance: 0.9,
          learningResources: [
            { type: 'course', title: 'System Design Fundamentals', provider: 'TechEd', duration: '8 weeks', cost: 299 },
            { type: 'book', title: 'Designing Data-Intensive Applications', provider: 'O\'Reilly', duration: '4 weeks' },
            { type: 'practice', title: 'Design challenges', provider: 'Internal', duration: 'ongoing' }
          ]
        },
        {
          skill: 'Leadership',
          currentLevel: 'beginner',
          targetLevel: 'intermediate',
          importance: 0.8,
          learningResources: [
            { type: 'workshop', title: 'Leadership Essentials', provider: 'HR', duration: '2 days' },
            { type: 'mentoring', title: 'Executive mentoring', provider: 'Internal', duration: '6 months' }
          ]
        }
      ],
      mentorship: {
        recommended: true,
        mentorProfiles: [
          { name: 'Sarah Johnson', role: 'Engineering Manager', expertise: ['Leadership', 'Career Growth'], matchScore: 0.92 },
          { name: 'Mike Chen', role: 'Senior Architect', expertise: ['System Design', 'Technical Leadership'], matchScore: 0.88 }
        ]
      },
      networking: {
        internalConnections: ['Engineering team leads', 'Product managers', 'Senior developers'],
        externalOpportunities: ['Tech meetups', 'Industry conferences', 'Professional associations'],
        events: ['Monthly tech talks', 'Quarterly engineering all-hands', 'Annual tech conference']
      }
    }
  }

  private async mockSkillsGapAnalysis(options: any): Promise<SkillsGapAnalysis> {
    await new Promise(resolve => setTimeout(resolve, 500))

    const currentSkills = options.currentSkills || [
      { name: 'JavaScript', level: 'advanced', verified: true },
      { name: 'React', level: 'advanced', verified: true },
      { name: 'Node.js', level: 'intermediate', verified: true },
      { name: 'PostgreSQL', level: 'intermediate', verified: false }
    ]

    const requiredSkills = options.requiredSkills || [
      { name: 'JavaScript', level: 'advanced', importance: 'critical' },
      { name: 'React', level: 'advanced', importance: 'critical' },
      { name: 'Node.js', level: 'advanced', importance: 'important' },
      { name: 'PostgreSQL', level: 'advanced', importance: 'important' },
      { name: 'AWS', level: 'intermediate', importance: 'important' },
      { name: 'Docker', level: 'intermediate', importance: 'nice_to_have' }
    ]

    const currentSkillMap = new Map<string, string>(currentSkills.map((s: any) => [s.name, s.level]))
    const gaps = requiredSkills
      .filter((req: any) => {
        const current: string | undefined = currentSkillMap.get(req.name)
        return !current || this.getSkillLevelValue(current || 'none') < this.getSkillLevelValue(req.level)
      })
      .map((req: any) => ({
        skill: req.name,
        currentLevel: currentSkillMap.get(req.name) || 'none',
        requiredLevel: req.level,
        priority: req.importance === 'critical' ? 3 : req.importance === 'important' ? 2 : 1,
        trainingRecommendations: [
          { type: 'online_course', provider: 'Udemy', duration: '4 weeks', cost: 99, effectiveness: 0.8 },
          { type: 'certification', provider: 'AWS', duration: '8 weeks', cost: 300, effectiveness: 0.9 },
          { type: 'hands_on_project', provider: 'Internal', duration: '6 weeks', effectiveness: 0.95 }
        ]
      }))

    const strengths = currentSkills
      .filter((skill: any) => skill.level === 'advanced' && skill.verified)
      .map((skill: any) => skill.name)

    const readinessScore = Math.round(
      ((requiredSkills.length - gaps.length) / requiredSkills.length) * 100
    )

    return {
      targetRole: options.targetRole,
      currentSkills,
      requiredSkills,
      gaps,
      strengths,
      readinessScore,
      estimatedTimeToReady: gaps.length > 0 ? `${gaps.length * 4}-${gaps.length * 6} weeks` : 'Ready now'
    }
  }

  private getSkillLevelValue(level: string): number {
    const levels = { 'none': 0, 'beginner': 1, 'intermediate': 2, 'advanced': 3, 'expert': 4 }
    return levels[level as keyof typeof levels] || 0
  }

  /**
   * Parse resume using AI with fallback to mock
   */
  private async parseResumeWithAI(
    file: Express.Multer.File,
    options: ResumeParseOptions
  ): Promise<ParsedResume> {
    try {
      // Extract text from file (simplified - in production would use proper PDF/Word parsing)
      const text = file.buffer.toString('utf-8')

      // Try AI parsing
      const aiResult = await this.aiProvider.parseResumeWithAI(text)

      return {
        id: uuidv4(),
        candidateName: aiResult.candidateName || options.candidateName,
        contact: aiResult.contact || {},
        summary: aiResult.summary,
        skills: aiResult.skills || [],
        experience: aiResult.experience || [],
        education: aiResult.education || [],
        certifications: aiResult.certifications || [],
        languages: aiResult.languages || [],
        metadata: {
          fileName: file.originalname,
          fileSize: file.size,
          parseDate: new Date().toISOString(),
          confidence: 0.95 // AI parsing confidence
        }
      }
    } catch (error) {
      logger.warn('AI resume parsing failed, falling back to mock:', error)
      return this.mockResumeParser(file, options)
    }
  }

  /**
   * Analyze sentiment using AI with fallback to mock
   */
  private async analyzeSentimentWithAI(
    text: string,
    context: any
  ): Promise<SentimentAnalysisResult> {
    try {
      const aiResult = await this.aiProvider.analyzeSentimentWithAI(text)

      return {
        id: uuidv4(),
        sentiment: aiResult.sentiment,
        confidence: aiResult.confidence || 0.8,
        score: aiResult.score || 0,
        emotions: aiResult.emotions || [],
        keywords: aiResult.keywords || [],
        summary: aiResult.summary || `The text expresses ${aiResult.sentiment} sentiment.`,
        recommendations: aiResult.sentiment === 'negative' ? [
          'Consider addressing the concerns raised',
          'Follow up with additional support'
        ] : []
      }
    } catch (error) {
      logger.warn('AI sentiment analysis failed, falling back to mock:', error)
      return this.mockSentimentAnalysis(text, context)
    }
  }

  /**
   * Get AI provider status
   */
  async getAIProviderStatus(): Promise<any> {
    try {
      return await this.aiProvider.getProviderStatus()
    } catch (error) {
      logger.error('Error getting AI provider status:', error)
      throw new Error('Failed to get AI provider status')
    }
  }
}
