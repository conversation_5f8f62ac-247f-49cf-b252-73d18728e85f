import { AIService } from '../../../src/services/aiService'
import { DatabaseService } from '../../../src/services/databaseService'

// Global type declarations
declare global {
  var cleanupDatabase: (() => Promise<void>) | undefined
}

// Mock dependencies
jest.mock('../../../src/services/databaseService')
jest.mock('../../../src/services/aiProviderService')

describe('AIService', () => {
  let aiService: AIService
  let mockDb: jest.Mocked<DatabaseService>

  beforeEach(() => {
    jest.clearAllMocks()
    mockDb = new DatabaseService() as jest.Mocked<DatabaseService>

    // Mock database responses
    mockDb.query = jest.fn()
    mockDb.transaction = jest.fn()

    // Inject mocked dependencies
    aiService = new AIService(mockDb)
  })

  afterEach(async () => {
    if (global.cleanupDatabase) {
      await global.cleanupDatabase()
    }
  })

  describe('parseResume', () => {
    const mockFile = {
      originalname: 'test-resume.pdf',
      mimetype: 'application/pdf',
      size: 1024,
      buffer: Buffer.from('test resume content')
    } as Express.Multer.File

    const mockOptions = {
      candidateName: 'John Doe',
      jobPositionId: 'test-job-id',
      extractSkills: true,
      extractExperience: true,
      extractEducation: true,
      extractContact: true
    }

    it('should parse resume successfully', async () => {
      // Mock database insert
      mockDb.query.mockResolvedValueOnce({
        rows: [{ id: 'test-resume-id' }],
        command: 'INSERT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      const result = await aiService.parseResume(mockFile, mockOptions, 'test-user-id')

      expect(result).toHaveProperty('id')
      expect(result).toHaveProperty('candidateName')
      expect(result).toHaveProperty('skills')
      expect(result).toHaveProperty('experience')
      expect(result).toHaveProperty('education')
      expect(mockDb.query).toHaveBeenCalled()
    })

    it('should handle database logging errors gracefully', async () => {
      // Mock the first call (logging) to fail, but subsequent calls to succeed
      mockDb.query
        .mockRejectedValueOnce(new Error('Database error'))
        .mockResolvedValueOnce({ rows: [], rowCount: 0, oid: 0, fields: [] })
        .mockResolvedValueOnce({ rows: [], rowCount: 0, oid: 0, fields: [] })

      // Should succeed even if logging fails
      const result = await aiService.parseResume(mockFile, mockOptions, 'test-user-id')

      expect(result).toHaveProperty('id')
      expect(result).toHaveProperty('candidateName')
      expect(result).toHaveProperty('skills')
      expect(result).toHaveProperty('experience')
      expect(result).toHaveProperty('education')
    })

    it('should validate file type', async () => {
      const invalidFile = {
        ...mockFile,
        mimetype: 'image/jpeg'
      } as Express.Multer.File

      await expect(
        aiService.parseResume(invalidFile, mockOptions, 'test-user-id')
      ).rejects.toThrow('Unsupported file type')
    })

    it('should validate file size', async () => {
      const largeFile = {
        ...mockFile,
        size: 20 * 1024 * 1024 // 20MB
      } as Express.Multer.File

      await expect(
        aiService.parseResume(largeFile, mockOptions, 'test-user-id')
      ).rejects.toThrow('File too large')
    })
  })

  describe('matchResumeToJob', () => {
    const mockResumeData = {
      id: 'test-resume-id',
      candidateName: 'John Doe',
      contact: { email: '<EMAIL>' },
      summary: 'Experienced developer',
      skills: [{ name: 'JavaScript', category: 'Programming', level: 'advanced' }],
      experience: [{ company: 'Tech Corp', position: 'Developer', startDate: '2020', endDate: '2023', description: 'Built web apps', skills: ['JavaScript'] }],
      education: [],
      certifications: [],
      languages: [],
      metadata: { fileName: 'resume.pdf', fileSize: 1024, parseDate: '2023-01-01', confidence: 0.95 }
    }

    const mockJobRequirements = {
      skills: ['JavaScript', 'React'],
      experience: '2-5 years',
      education: 'Bachelor degree preferred'
    }

    const mockWeightings = {
      skills: 0.5,
      experience: 0.3,
      education: 0.2
    }

    it('should match resume to job successfully', async () => {
      // Mock logging call
      mockDb.query.mockResolvedValueOnce({
        rows: [],
        command: 'INSERT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      // Mock resume data
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          id: 'test-resume-id',
          skills: JSON.stringify([
            { name: 'JavaScript', category: 'Programming', level: 'advanced' }
          ]),
          experience: JSON.stringify([])
        }],
        command: 'SELECT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      // Mock job data
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          id: 'test-job-id',
          required_skills: JSON.stringify(['JavaScript', 'React']),
          experience_required: '2-5 years'
        }],
        command: 'SELECT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      // Mock final logging call
      mockDb.query.mockResolvedValueOnce({
        rows: [],
        command: 'UPDATE',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      const result = await aiService.matchResumeToJob(mockResumeData, mockJobRequirements, mockWeightings, 'test-user-id')

      expect(result).toHaveProperty('matchId')
      expect(result).toHaveProperty('overallScore')
      expect(result).toHaveProperty('skillsMatch')
      expect(result).toHaveProperty('experienceMatch')
      expect(result.overallScore).toBeGreaterThan(0)
      expect(result.overallScore).toBeLessThanOrEqual(100)
    })

    it('should handle missing resume', async () => {
      // Mock logging call
      mockDb.query.mockResolvedValueOnce({
        rows: [],
        command: 'INSERT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      // Mock resume not found
      mockDb.query.mockResolvedValueOnce({
        rows: [],
        command: 'SELECT',
        rowCount: 0,
        oid: 0,
        fields: []
      })

      await expect(
        aiService.matchResumeToJob(mockResumeData, mockJobRequirements, mockWeightings, 'test-user-id')
      ).rejects.toThrow('Resume not found')
    })

    it('should handle missing job', async () => {
      // Mock resume found
      mockDb.query.mockResolvedValueOnce({
        rows: [{ id: 'test-resume-id', skills: '[]', experience: '[]' }],
        command: 'SELECT',
        rowCount: 1,
        oid: 0,
        fields: []
      })
      // Mock job not found
      mockDb.query.mockResolvedValueOnce({
        rows: [],
        command: 'SELECT',
        rowCount: 0,
        oid: 0,
        fields: []
      })

      await expect(
        aiService.matchResumeToJob(mockResumeData, mockJobRequirements, mockWeightings, 'test-user-id')
      ).rejects.toThrow('Job not found')
    })
  })

  describe('analyzeSentiment', () => {
    const mockSentimentOptions = {
      text: 'I really enjoy working here. The team is great!',
      context: {
        type: 'employee_feedback',
        anonymous: false
      }
    }

    it('should analyze sentiment successfully', async () => {
      mockDb.query.mockResolvedValueOnce({
        rows: [{ id: 'test-sentiment-id' }],
        command: 'INSERT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      const result = await aiService.analyzeSentiment(
        mockSentimentOptions.text,
        mockSentimentOptions.context,
        'test-user-id'
      )

      expect(result).toHaveProperty('id')
      expect(result).toHaveProperty('sentiment')
      expect(result).toHaveProperty('confidence')
      expect(result).toHaveProperty('score')
      expect(['positive', 'negative', 'neutral']).toContain(result.sentiment)
      expect(result.confidence).toBeGreaterThanOrEqual(0)
      expect(result.confidence).toBeLessThanOrEqual(1)
    })

    it('should handle empty text', async () => {
      await expect(
        aiService.analyzeSentiment('', mockSentimentOptions.context, 'test-user-id')
      ).rejects.toThrow('Text is required for sentiment analysis')
    })

    it('should handle very long text', async () => {
      const longText = 'a'.repeat(10000)
      
      await expect(
        aiService.analyzeSentiment(longText, mockSentimentOptions.context, 'test-user-id')
      ).rejects.toThrow('Text too long for sentiment analysis')
    })
  })

  describe('predictAttrition', () => {
    const mockAttritionOptions = {
      timeframe: '6_months' as const,
      departments: ['engineering', 'sales'],
      includeFactors: true
    }

    it('should predict attrition successfully', async () => {
      // Mock employee data
      mockDb.query.mockResolvedValueOnce({
        rows: [
          { id: 'emp1', department: 'engineering', tenure_months: 24, performance_score: 85 },
          { id: 'emp2', department: 'sales', tenure_months: 6, performance_score: 70 }
        ],
        command: 'SELECT',
        rowCount: 2,
        oid: 0,
        fields: []
      })

      mockDb.query.mockResolvedValueOnce({
        rows: [{ id: 'test-prediction-id' }],
        command: 'INSERT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      const result = await aiService.predictAttrition(mockAttritionOptions, 'test-user-id')

      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBeGreaterThan(0)
      expect(result[0]).toHaveProperty('employeeId')
      expect(result[0]).toHaveProperty('riskLevel')
      expect(['low', 'medium', 'high', 'critical']).toContain(result[0].riskLevel)
    })

    it('should handle invalid timeframe', async () => {
      const invalidOptions = {
        ...mockAttritionOptions,
        timeframe: 'invalid' as any
      }

      await expect(
        aiService.predictAttrition(invalidOptions, 'test-user-id')
      ).rejects.toThrow('Invalid timeframe')
    })
  })

  describe('generatePerformanceInsights', () => {
    const mockInsightsOptions = {
      timeframe: 'quarter' as const,
      includeGoals: true,
      includePeerComparison: true
    }

    it('should generate performance insights successfully', async () => {
      // Mock employee data
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          id: 'test-employee-id',
          first_name: 'John',
          last_name: 'Doe',
          department: 'Engineering'
        }],
        command: 'SELECT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      // Mock performance data
      mockDb.query.mockResolvedValueOnce({
        rows: [
          { metric: 'productivity', value: 85, date: new Date() },
          { metric: 'quality', value: 90, date: new Date() }
        ],
        command: 'SELECT',
        rowCount: 2,
        oid: 0,
        fields: []
      })

      mockDb.query.mockResolvedValueOnce({
        rows: [{ id: 'test-insights-id' }],
        command: 'INSERT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      const result = await aiService.generatePerformanceInsights('test-employee-id', mockInsightsOptions, 'test-user-id')

      expect(result).toHaveProperty('employeeId')
      expect(result).toHaveProperty('overallScore')
      expect(result).toHaveProperty('trends')
      expect(result).toHaveProperty('strengths')
      expect(result).toHaveProperty('improvementAreas')
      expect(Array.isArray(result.strengths)).toBe(true)
      expect(Array.isArray(result.improvementAreas)).toBe(true)
    })

    it('should handle missing employee', async () => {
      mockDb.query.mockResolvedValueOnce({
        rows: [],
        command: 'SELECT',
        rowCount: 0,
        oid: 0,
        fields: []
      })

      await expect(
        aiService.generatePerformanceInsights('test-employee-id', mockInsightsOptions, 'test-user-id')
      ).rejects.toThrow('Employee not found')
    })
  })

  describe('generateCareerRecommendations', () => {
    const mockCareerOptions = {
      includeSkillGaps: true,
      includeLearningPaths: true
    }

    it('should generate career recommendations successfully', async () => {
      // Mock employee data
      mockDb.query.mockResolvedValueOnce({
        rows: [{
          id: 'test-employee-id',
          current_role: 'Software Engineer',
          skills: JSON.stringify([
            { name: 'JavaScript', level: 'advanced' }
          ])
        }],
        command: 'SELECT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      mockDb.query.mockResolvedValueOnce({
        rows: [{ id: 'test-recommendations-id' }],
        command: 'INSERT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      const result = await aiService.generateCareerRecommendations('test-employee-id', mockCareerOptions, 'test-user-id')

      expect(result).toHaveProperty('employeeId')
      expect(result).toHaveProperty('currentRole')
      expect(result).toHaveProperty('careerPath')
      expect(result).toHaveProperty('skillDevelopment')
      expect(result).toHaveProperty('mentorship')
      expect(Array.isArray(result.careerPath)).toBe(true)
      expect(Array.isArray(result.skillDevelopment)).toBe(true)
      expect(Array.isArray(result.careerPath)).toBe(true)
    })
  })

  describe('performSkillsGapAnalysis', () => {
    const mockSkillsOptions = {
      department: 'engineering',
      includeTrainingRecommendations: true
    }

    it('should analyze skills gap successfully', async () => {
      // Mock department employees
      mockDb.query.mockResolvedValueOnce({
        rows: [
          { id: 'emp1', skills: JSON.stringify([{ name: 'JavaScript', level: 'advanced' }]) },
          { id: 'emp2', skills: JSON.stringify([{ name: 'Python', level: 'intermediate' }]) }
        ],
        command: 'SELECT',
        rowCount: 2,
        oid: 0,
        fields: []
      })

      mockDb.query.mockResolvedValueOnce({
        rows: [{ id: 'test-analysis-id' }],
        command: 'INSERT',
        rowCount: 1,
        oid: 0,
        fields: []
      })

      const result = await aiService.performSkillsGapAnalysis(mockSkillsOptions, 'test-user-id')

      expect(result).toHaveProperty('currentSkills')
      expect(result).toHaveProperty('requiredSkills')
      expect(result).toHaveProperty('gaps')
      expect(result).toHaveProperty('readinessScore')
      expect(Array.isArray(result.gaps)).toBe(true)
      expect(Array.isArray(result.currentSkills)).toBe(true)
    })
  })

  describe('getAIProviderStatus', () => {
    it('should return AI provider status', async () => {
      const result = await aiService.getAIProviderStatus()

      expect(result).toHaveProperty('resumeParsing')
      expect(result).toHaveProperty('sentimentAnalysis')
      expect(result.resumeParsing).toHaveProperty('enabled')
      expect(result.resumeParsing).toHaveProperty('configured')
    })

    it('should handle provider service errors', async () => {
      // Mock provider service to throw error
      const mockAIProvider = aiService['aiProvider']
      mockAIProvider.getProviderStatus = jest.fn().mockRejectedValue(new Error('Provider error'))

      await expect(
        aiService.getAIProviderStatus()
      ).rejects.toThrow('Failed to get AI provider status')
    })
  })
})
