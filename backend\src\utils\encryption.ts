import crypto from 'crypto'
import { logger } from './logger'

// Configuration for field-level encryption
const ALGORITHM = 'aes-256-gcm'
const KEY_LENGTH = 32 // 256 bits
const IV_LENGTH = 16 // 128 bits
const TAG_LENGTH = 16 // 128 bits

// PII fields that require encryption
export const ENCRYPTED_FIELDS = [
  'first_name',
  'last_name', 
  'phone',
  'personal_email',
  'date_of_birth',
  'national_id',
  'bank_account',
  'address',
  'emergency_contact',
  'salary',
  'notes'
] as const

export type EncryptedField = typeof ENCRYPTED_FIELDS[number]

interface EncryptedData {
  encrypted: string
  iv: string
  tag: string
}

class FieldEncryption {
  private encryptionKey: Buffer

  constructor() {
    const keyString = process.env.FIELD_ENCRYPTION_KEY
    if (!keyString) {
      throw new Error('FIELD_ENCRYPTION_KEY environment variable is required')
    }

    // Derive key from environment variable
    this.encryptionKey = crypto.scryptSync(keyString, 'salt', KEY_LENGTH)
  }

  /**
   * Encrypt a field value
   */
  encrypt(plaintext: string | null): string | null {
    if (!plaintext || plaintext === '') {
      return plaintext
    }

    try {
      const iv = crypto.randomBytes(IV_LENGTH)
      const cipher = crypto.createCipher(ALGORITHM, this.encryptionKey)
      cipher.setAAD(Buffer.from('peoplenest-hrms'))

      let encrypted = cipher.update(plaintext, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      const tag = cipher.getAuthTag()

      const result: EncryptedData = {
        encrypted,
        iv: iv.toString('hex'),
        tag: tag.toString('hex')
      }

      return Buffer.from(JSON.stringify(result)).toString('base64')
    } catch (error) {
      logger.error('Encryption failed:', error)
      throw new Error('Failed to encrypt field data')
    }
  }

  /**
   * Decrypt a field value
   */
  decrypt(ciphertext: string | null): string | null {
    if (!ciphertext || ciphertext === '') {
      return ciphertext
    }

    try {
      const data: EncryptedData = JSON.parse(Buffer.from(ciphertext, 'base64').toString())

      const decipher = crypto.createDecipher(ALGORITHM, this.encryptionKey)
      decipher.setAAD(Buffer.from('peoplenest-hrms'))
      decipher.setAuthTag(Buffer.from(data.tag, 'hex'))

      let decrypted = decipher.update(data.encrypted, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return decrypted
    } catch (error) {
      logger.error('Decryption failed:', error)
      throw new Error('Failed to decrypt field data')
    }
  }

  /**
   * Encrypt multiple fields in an object
   */
  encryptFields<T extends Record<string, any>>(data: T, fieldsToEncrypt: string[] = []): T {
    const result = { ...data }
    const fields = fieldsToEncrypt.length > 0 ? fieldsToEncrypt : ENCRYPTED_FIELDS

    for (const field of fields) {
      if (field in result && result[field] !== null && result[field] !== undefined) {
        try {
          if (typeof result[field] === 'object') {
            // Handle nested objects (like address, emergency_contact)
            ;(result as any)[field] = this.encrypt(JSON.stringify(result[field]))
          } else {
            ;(result as any)[field] = this.encrypt(String(result[field]))
          }
        } catch (error) {
          logger.error(`Failed to encrypt field ${field}:`, error)
          // Don't fail the entire operation, but log the error
        }
      }
    }

    return result
  }

  /**
   * Decrypt multiple fields in an object
   */
  decryptFields<T extends Record<string, any>>(data: T, fieldsToDecrypt: string[] = []): T {
    const result = { ...data }
    const fields = fieldsToDecrypt.length > 0 ? fieldsToDecrypt : ENCRYPTED_FIELDS

    for (const field of fields) {
      if (field in result && result[field] !== null && result[field] !== undefined) {
        try {
          const decrypted = this.decrypt(result[field])
          if (decrypted && (field === 'address' || field === 'emergency_contact')) {
            // Parse JSON for nested objects
            try {
              ;(result as any)[field] = JSON.parse(decrypted)
            } catch {
              ;(result as any)[field] = decrypted
            }
          } else {
            ;(result as any)[field] = decrypted
          }
        } catch (error) {
          logger.error(`Failed to decrypt field ${field}:`, error)
          // Don't fail the entire operation, but log the error
        }
      }
    }

    return result
  }

  /**
   * Check if a field should be encrypted
   */
  shouldEncrypt(fieldName: string): boolean {
    return ENCRYPTED_FIELDS.includes(fieldName as EncryptedField)
  }

  /**
   * Generate a new encryption key (for key rotation)
   */
  static generateKey(): string {
    return crypto.randomBytes(KEY_LENGTH).toString('hex')
  }

  /**
   * Hash sensitive data for searching (one-way)
   */
  hashForSearch(value: string): string {
    return crypto.createHash('sha256')
      .update(value + process.env.SEARCH_HASH_SALT || 'default-salt')
      .digest('hex')
  }
}

// Singleton instance
export const fieldEncryption = new FieldEncryption()

/**
 * Middleware to automatically encrypt/decrypt fields in database operations
 */
export const encryptionMiddleware = {
  /**
   * Encrypt fields before database insert/update
   */
  beforeSave: <T extends Record<string, any>>(data: T, tableName: string): T => {
    // Only encrypt for tables that contain PII
    const piiTables = ['employees', 'employee_profiles', 'payroll_records']
    
    if (!piiTables.includes(tableName)) {
      return data
    }

    logger.debug(`Encrypting fields for table: ${tableName}`)
    return fieldEncryption.encryptFields(data)
  },

  /**
   * Decrypt fields after database select
   */
  afterLoad: <T extends Record<string, any>>(data: T, tableName: string): T => {
    // Only decrypt for tables that contain PII
    const piiTables = ['employees', 'employee_profiles', 'payroll_records']
    
    if (!piiTables.includes(tableName)) {
      return data
    }

    logger.debug(`Decrypting fields for table: ${tableName}`)
    return fieldEncryption.decryptFields(data)
  }
}

/**
 * Utility for secure field comparison (for encrypted fields)
 */
export const secureFieldComparison = {
  /**
   * Compare encrypted field with plaintext value
   */
  compare: (encryptedValue: string, plaintextValue: string): boolean => {
    try {
      const decrypted = fieldEncryption.decrypt(encryptedValue)
      return decrypted === plaintextValue
    } catch {
      return false
    }
  },

  /**
   * Search in encrypted fields using hash comparison
   */
  searchHash: (plaintextValue: string): string => {
    return fieldEncryption.hashForSearch(plaintextValue)
  }
}

/**
 * Key rotation utilities
 */
export const keyRotation = {
  /**
   * Rotate encryption key for a specific table
   */
  rotateTableKey: async (tableName: string, oldKey: string, newKey: string): Promise<void> => {
    // This would be implemented to re-encrypt all data with new key
    logger.info(`Starting key rotation for table: ${tableName}`)
    // Implementation would depend on specific database operations
    throw new Error('Key rotation not yet implemented')
  },

  /**
   * Validate encryption key strength
   */
  validateKey: (key: string): boolean => {
    return key.length >= 64 && /^[a-f0-9]+$/i.test(key)
  }
}

export default fieldEncryption
