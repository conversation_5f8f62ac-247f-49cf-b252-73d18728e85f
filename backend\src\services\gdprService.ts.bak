import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'
import { auditLogger } from '../utils/auditLogger'
import { fieldEncryption } from '../utils/encryption'

const db = new DatabaseService()

export type GDPRRequestType = 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction'
export type RequestStatus = 'pending' | 'in_progress' | 'completed' | 'rejected'

export interface DataSubjectRequest {
  id?: string
  employee_id: string
  request_type: GDPRRequestType
  status: RequestStatus
  requested_data?: string[]
  response_data?: any
  requested_by: string
  processed_by?: string
  requested_at?: Date
  processed_at?: Date
  due_date?: Date
  reason?: string
  rejection_reason?: string
}

export interface ConsentRecord {
  id?: string
  employee_id: string
  consent_type: string
  consent_given: boolean
  consent_date: Date
  withdrawal_date?: Date
  purpose: string
  legal_basis: string
  data_categories: string[]
  retention_period: number
  third_parties?: string[]
}

class GDPRService {
  /**
   * Create a new data subject request
   */
  async createDataSubjectRequest(request: Omit<DataSubjectRequest, 'id' | 'status' | 'requested_at' | 'due_date'>): Promise<DataSubjectRequest> {
    try {
      const dueDate = new Date()
      dueDate.setDate(dueDate.getDate() + 30) // 30 days to respond

      const query = `
        INSERT INTO data_subject_requests (
          employee_id, request_type, status, requested_data, requested_by, 
          requested_at, due_date, reason
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `

      const values = [
        request.employee_id,
        request.request_type,
        'pending',
        request.requested_data ? JSON.stringify(request.requested_data) : null,
        request.requested_by,
        new Date(),
        dueDate,
        request.reason
      ]

      const result = await db.query(query, values)
      const createdRequest = result.rows[0]

      // Log the request creation
      await auditLogger.logAuditEvent({
        table_name: 'data_subject_requests',
        record_id: createdRequest.id,
        action: 'CREATE',
        new_values: createdRequest,
        user_id: request.requested_by,
        data_classification: 'RESTRICTED',
        legal_basis: 'GDPR Article 12-22',
        purpose: 'Data Subject Rights Management'
      })

      logger.info('GDPR data subject request created', {
        requestId: createdRequest.id,
        employeeId: request.employee_id,
        requestType: request.request_type,
        requestedBy: request.requested_by
      })

      return {
        ...createdRequest,
        requested_data: createdRequest.requested_data ? JSON.parse(createdRequest.requested_data) : null
      }
    } catch (error) {
      logger.error('Failed to create data subject request:', error)
      throw new Error('Failed to create data subject request')
    }
  }

  /**
   * Process a data access request (Article 15)
   */
  async processAccessRequest(requestId: string, processedBy: string): Promise<any> {
    try {
      // Get the request details
      const requestQuery = `
        SELECT * FROM data_subject_requests 
        WHERE id = $1 AND request_type = 'access'
      `
      const requestResult = await db.query(requestQuery, [requestId])
      
      if (requestResult.rows.length === 0) {
        throw new Error('Access request not found')
      }

      const request = requestResult.rows[0]
      const employeeId = request.employee_id

      // Collect all personal data for the employee
      const personalData = await this.collectPersonalData(employeeId)

      // Update request status
      const updateQuery = `
        UPDATE data_subject_requests 
        SET status = 'completed', processed_by = $1, processed_at = $2, response_data = $3
        WHERE id = $4
        RETURNING *
      `

      const updateResult = await db.query(updateQuery, [
        processedBy,
        new Date(),
        JSON.stringify(personalData),
        requestId
      ])

      // Log the processing
      await auditLogger.logAuditEvent({
        table_name: 'data_subject_requests',
        record_id: requestId,
        action: 'UPDATE',
        old_values: request,
        new_values: updateResult.rows[0],
        user_id: processedBy,
        data_classification: 'RESTRICTED',
        legal_basis: 'GDPR Article 15',
        purpose: 'Data Access Request Processing'
      })

      return personalData
    } catch (error) {
      logger.error('Failed to process access request:', error)
      throw new Error('Failed to process access request')
    }
  }

  /**
   * Process a data erasure request (Article 17 - Right to be Forgotten)
   */
  async processErasureRequest(requestId: string, processedBy: string): Promise<void> {
    try {
      const requestQuery = `
        SELECT * FROM data_subject_requests 
        WHERE id = $1 AND request_type = 'erasure'
      `
      const requestResult = await db.query(requestQuery, [requestId])
      
      if (requestResult.rows.length === 0) {
        throw new Error('Erasure request not found')
      }

      const request = requestResult.rows[0]
      const employeeId = request.employee_id

      // Check if erasure is legally permissible
      const canErase = await this.checkErasurePermissibility(employeeId)
      
      if (!canErase.allowed) {
        // Reject the request
        await db.query(`
          UPDATE data_subject_requests 
          SET status = 'rejected', processed_by = $1, processed_at = $2, rejection_reason = $3
          WHERE id = $4
        `, [processedBy, new Date(), canErase.reason, requestId])
        
        throw new Error(`Erasure request rejected: ${canErase.reason}`)
      }

      // Perform data erasure
      await this.erasePersonalData(employeeId, processedBy)

      // Update request status
      await db.query(`
        UPDATE data_subject_requests 
        SET status = 'completed', processed_by = $1, processed_at = $2
        WHERE id = $3
      `, [processedBy, new Date(), requestId])

      logger.info('Data erasure completed', {
        requestId,
        employeeId,
        processedBy
      })
    } catch (error) {
      logger.error('Failed to process erasure request:', error)
      throw error
    }
  }

  /**
   * Process data portability request (Article 20)
   */
  async processPortabilityRequest(requestId: string, processedBy: string): Promise<any> {
    try {
      const requestQuery = `
        SELECT * FROM data_subject_requests 
        WHERE id = $1 AND request_type = 'portability'
      `
      const requestResult = await db.query(requestQuery, [requestId])
      
      if (requestResult.rows.length === 0) {
        throw new Error('Portability request not found')
      }

      const request = requestResult.rows[0]
      const employeeId = request.employee_id

      // Collect portable data (structured, commonly used formats)
      const portableData = await this.collectPortableData(employeeId)

      // Update request status
      await db.query(`
        UPDATE data_subject_requests 
        SET status = 'completed', processed_by = $1, processed_at = $2, response_data = $3
        WHERE id = $4
      `, [processedBy, new Date(), JSON.stringify(portableData), requestId])

      return portableData
    } catch (error) {
      logger.error('Failed to process portability request:', error)
      throw new Error('Failed to process portability request')
    }
  }

  /**
   * Record consent
   */
  async recordConsent(consent: Omit<ConsentRecord, 'id' | 'consent_date'>): Promise<ConsentRecord> {
    try {
      const query = `
        INSERT INTO consent_records (
          employee_id, consent_type, consent_given, consent_date, purpose,
          legal_basis, data_categories, retention_period, third_parties
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `

      const values = [
        consent.employee_id,
        consent.consent_type,
        consent.consent_given,
        new Date(),
        consent.purpose,
        consent.legal_basis,
        JSON.stringify(consent.data_categories),
        consent.retention_period,
        consent.third_parties ? JSON.stringify(consent.third_parties) : null
      ]

      const result = await db.query(query, values)
      const consentRecord = result.rows[0]

      // Log consent recording
      await auditLogger.logAuditEvent({
        table_name: 'consent_records',
        record_id: consentRecord.id,
        action: 'CREATE',
        new_values: consentRecord,
        user_id: consent.employee_id,
        data_classification: 'RESTRICTED',
        legal_basis: 'GDPR Article 7',
        purpose: 'Consent Management'
      })

      return {
        ...consentRecord,
        data_categories: JSON.parse(consentRecord.data_categories),
        third_parties: consentRecord.third_parties ? JSON.parse(consentRecord.third_parties) : null
      }
    } catch (error) {
      logger.error('Failed to record consent:', error)
      throw new Error('Failed to record consent')
    }
  }

  /**
   * Withdraw consent
   */
  async withdrawConsent(consentId: string, employeeId: string): Promise<void> {
    try {
      const query = `
        UPDATE consent_records 
        SET consent_given = false, withdrawal_date = $1
        WHERE id = $2 AND employee_id = $3
      `

      await db.query(query, [new Date(), consentId, employeeId])

      // Log consent withdrawal
      await auditLogger.logAuditEvent({
        table_name: 'consent_records',
        record_id: consentId,
        action: 'UPDATE',
        new_values: { consent_given: false, withdrawal_date: new Date() },
        user_id: employeeId,
        data_classification: 'RESTRICTED',
        legal_basis: 'GDPR Article 7',
        purpose: 'Consent Withdrawal'
      })

      logger.info('Consent withdrawn', { consentId, employeeId })
    } catch (error) {
      logger.error('Failed to withdraw consent:', error)
      throw new Error('Failed to withdraw consent')
    }
  }

  /**
   * Get all requests for an employee
   */
  async getEmployeeRequests(employeeId: string): Promise<DataSubjectRequest[]> {
    try {
      const query = `
        SELECT * FROM data_subject_requests 
        WHERE employee_id = $1 
        ORDER BY requested_at DESC
      `

      const result = await db.query(query, [employeeId])
      return result.rows.map(row => ({
        ...row,
        requested_data: row.requested_data ? JSON.parse(row.requested_data) : null,
        response_data: row.response_data ? JSON.parse(row.response_data) : null
      }))
    } catch (error) {
      logger.error('Failed to get employee requests:', error)
      return []
    }
  }

  /**
   * Collect all personal data for an employee
   */
  private async collectPersonalData(employeeId: string): Promise<any> {
    const data: any = {}

    try {
      // Employee profile data
      const employeeQuery = `SELECT * FROM employees WHERE id = $1`
      const employeeResult = await db.query(employeeQuery, [employeeId])
      if (employeeResult.rows.length > 0) {
        data.profile = fieldEncryption.decryptFields(employeeResult.rows[0])
      }

      // Payroll data
      const payrollQuery = `SELECT * FROM payroll_records WHERE employee_id = $1`
      const payrollResult = await db.query(payrollQuery, [employeeId])
      data.payroll = payrollResult.rows.map(row => fieldEncryption.decryptFields(row))

      // Performance reviews
      const reviewsQuery = `SELECT * FROM performance_reviews WHERE employee_id = $1`
      const reviewsResult = await db.query(reviewsQuery, [employeeId])
      data.performance_reviews = reviewsResult.rows

      // Training records
      const trainingQuery = `SELECT * FROM training_records WHERE employee_id = $1`
      const trainingResult = await db.query(trainingQuery, [employeeId])
      data.training = trainingResult.rows

      // Consent records
      const consentQuery = `SELECT * FROM consent_records WHERE employee_id = $1`
      const consentResult = await db.query(consentQuery, [employeeId])
      data.consents = consentResult.rows

      return data
    } catch (error) {
      logger.error('Failed to collect personal data:', error)
      throw new Error('Failed to collect personal data')
    }
  }

  /**
   * Collect portable data in structured format
   */
  private async collectPortableData(employeeId: string): Promise<any> {
    // Similar to collectPersonalData but formatted for portability
    const data = await this.collectPersonalData(employeeId)
    
    // Format for portability (JSON, CSV, XML)
    return {
      format: 'JSON',
      exported_at: new Date().toISOString(),
      data_subject: employeeId,
      data: data
    }
  }

  /**
   * Check if data erasure is legally permissible
   */
  private async checkErasurePermissibility(employeeId: string): Promise<{ allowed: boolean; reason?: string }> {
    try {
      // Check for legal obligations that prevent erasure
      const payrollQuery = `
        SELECT COUNT(*) as count FROM payroll_records 
        WHERE employee_id = $1 AND created_at > NOW() - INTERVAL '7 years'
      `
      const payrollResult = await db.query(payrollQuery, [employeeId])
      
      if (parseInt(payrollResult.rows[0].count) > 0) {
        return {
          allowed: false,
          reason: 'Cannot erase data due to legal obligation to retain payroll records for 7 years'
        }
      }

      // Check for ongoing legal proceedings
      // This would be implemented based on specific business requirements

      return { allowed: true }
    } catch (error) {
      logger.error('Failed to check erasure permissibility:', error)
      return {
        allowed: false,
        reason: 'Unable to verify erasure permissibility'
      }
    }
  }

  /**
   * Erase personal data (anonymization)
   */
  private async erasePersonalData(employeeId: string, processedBy: string): Promise<void> {
    try {
      // Anonymize employee data instead of deletion to maintain referential integrity
      const anonymizedData = {
        first_name: fieldEncryption.encrypt('ANONYMIZED'),
        last_name: fieldEncryption.encrypt('ANONYMIZED'),
        email: `anonymized_${employeeId}@deleted.local`,
        phone: fieldEncryption.encrypt('ANONYMIZED'),
        date_of_birth: null,
        address: fieldEncryption.encrypt('ANONYMIZED'),
        emergency_contact: fieldEncryption.encrypt('ANONYMIZED'),
        status: 'terminated',
        anonymized_at: new Date(),
        anonymized_by: processedBy
      }

      await db.query(`
        UPDATE employees 
        SET first_name = $1, last_name = $2, email = $3, phone = $4, 
            date_of_birth = $5, address = $6, emergency_contact = $7,
            status = $8, anonymized_at = $9, anonymized_by = $10
        WHERE id = $11
      `, [
        anonymizedData.first_name,
        anonymizedData.last_name,
        anonymizedData.email,
        anonymizedData.phone,
        anonymizedData.date_of_birth,
        anonymizedData.address,
        anonymizedData.emergency_contact,
        anonymizedData.status,
        anonymizedData.anonymized_at,
        anonymizedData.anonymized_by,
        employeeId
      ])

      // Log the erasure
      await auditLogger.logAuditEvent({
        table_name: 'employees',
        record_id: employeeId,
        action: 'UPDATE',
        new_values: { status: 'anonymized' },
        user_id: processedBy,
        data_classification: 'RESTRICTED',
        legal_basis: 'GDPR Article 17',
        purpose: 'Data Erasure Request'
      })
    } catch (error) {
      logger.error('Failed to erase personal data:', error)
      throw new Error('Failed to erase personal data')
    }
  }
}

export const gdprService = new GDPRService()
export default gdprService
