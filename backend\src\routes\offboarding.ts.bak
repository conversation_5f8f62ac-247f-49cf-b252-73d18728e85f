import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { OffboardingService } from '../services/offboardingService'
import { requirePermission } from '../middleware/permissions'
import { logger } from '../utils/logger'

const router = express.Router()
const offboardingService = new OffboardingService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    })
  }
  next()
}

/**
 * @route POST /api/offboarding/initiate/:employeeId
 * @desc Initiate offboarding process for employee
 * @access HR, Manager
 */
router.post('/initiate/:employeeId',
  requirePermission('hr', 'manager'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    body('terminationType').isIn(['voluntary', 'involuntary', 'retirement', 'layoff']).withMessage('Invalid termination type'),
    body('lastWorkingDay').isISO8601().withMessage('Valid last working day is required'),
    body('reason').notEmpty().withMessage('Reason is required'),
    body('exitInterviewRequired').isBoolean(),
    body('knowledgeTransferRequired').isBoolean(),
    body('assetReturnRequired').isBoolean(),
    body('customTasks').optional().isArray(),
    body('notes').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const offboarding = await offboardingService.initiateOffboarding(
        employeeId,
        req.body,
        req.user.id
      )
      
      logger.info(`Offboarding initiated for employee: ${employeeId}`, { 
        userId: req.user.id,
        offboardingId: offboarding.id 
      })
      
      res.status(201).json(offboarding)
    } catch (error) {
      logger.error('Error initiating offboarding:', error)
      res.status(500).json({ error: 'Failed to initiate offboarding process' })
    }
  }
)

/**
 * @route GET /api/offboarding/:employeeId
 * @desc Get offboarding status for employee
 * @access HR, Manager
 */
router.get('/:employeeId',
  requirePermission('hr', 'manager'),
  [param('employeeId').isUUID().withMessage('Invalid employee ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const offboarding = await offboardingService.getOffboardingStatus(employeeId)
      
      if (!offboarding) {
        return res.status(404).json({ error: 'Offboarding process not found' })
      }
      
      res.json(offboarding)
    } catch (error) {
      logger.error('Error fetching offboarding status:', error)
      res.status(500).json({ error: 'Failed to fetch offboarding status' })
    }
  }
)

/**
 * @route PUT /api/offboarding/:employeeId/tasks/:taskId/complete
 * @desc Mark offboarding task as complete
 * @access HR, Manager
 */
router.put('/:employeeId/tasks/:taskId/complete',
  requirePermission('hr', 'manager'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    param('taskId').isUUID().withMessage('Invalid task ID'),
    body('notes').optional().isString(),
    body('attachments').optional().isArray(),
    body('verificationRequired').optional().isBoolean()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId, taskId } = req.params
      const task = await offboardingService.completeTask(
        taskId,
        req.body,
        req.user.id
      )
      
      logger.info(`Offboarding task completed: ${taskId}`, { 
        userId: req.user.id,
        employeeId 
      })
      
      res.json(task)
    } catch (error) {
      logger.error('Error completing offboarding task:', error)
      res.status(500).json({ error: 'Failed to complete offboarding task' })
    }
  }
)

/**
 * @route POST /api/offboarding/:employeeId/exit-interview
 * @desc Schedule or conduct exit interview
 * @access HR
 */
router.post('/:employeeId/exit-interview',
  requirePermission('hr'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    body('scheduledDate').optional().isISO8601(),
    body('interviewerIds').isArray().withMessage('Interviewer IDs must be an array'),
    body('questions').optional().isArray(),
    body('responses').optional().isObject(),
    body('feedback').optional().isString(),
    body('recommendations').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const exitInterview = await offboardingService.handleExitInterview(
        employeeId,
        req.body,
        req.user.id
      )
      
      logger.info(`Exit interview processed for employee: ${employeeId}`, { 
        userId: req.user.id 
      })
      
      res.json(exitInterview)
    } catch (error) {
      logger.error('Error processing exit interview:', error)
      res.status(500).json({ error: 'Failed to process exit interview' })
    }
  }
)

/**
 * @route POST /api/offboarding/:employeeId/knowledge-transfer
 * @desc Manage knowledge transfer process
 * @access HR, Manager
 */
router.post('/:employeeId/knowledge-transfer',
  requirePermission('hr', 'manager'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    body('transferToEmployeeId').isUUID().withMessage('Transfer recipient is required'),
    body('knowledgeAreas').isArray().withMessage('Knowledge areas must be an array'),
    body('documents').optional().isArray(),
    body('sessions').optional().isArray(),
    body('deadline').isISO8601().withMessage('Valid deadline is required'),
    body('notes').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const knowledgeTransfer = await offboardingService.manageKnowledgeTransfer(
        employeeId,
        req.body,
        req.user.id
      )
      
      logger.info(`Knowledge transfer initiated for employee: ${employeeId}`, { 
        userId: req.user.id,
        transferTo: req.body.transferToEmployeeId 
      })
      
      res.json(knowledgeTransfer)
    } catch (error) {
      logger.error('Error managing knowledge transfer:', error)
      res.status(500).json({ error: 'Failed to manage knowledge transfer' })
    }
  }
)

/**
 * @route POST /api/offboarding/:employeeId/asset-return
 * @desc Track asset return process
 * @access HR, IT
 */
router.post('/:employeeId/asset-return',
  requirePermission('hr', 'it'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    body('assets').isArray().withMessage('Assets must be an array'),
    body('assets.*.assetId').notEmpty().withMessage('Asset ID is required'),
    body('assets.*.assetType').isIn(['laptop', 'phone', 'tablet', 'monitor', 'keyboard', 'mouse', 'headset', 'other']),
    body('assets.*.condition').isIn(['excellent', 'good', 'fair', 'poor', 'damaged']),
    body('assets.*.returnDate').optional().isISO8601(),
    body('assets.*.notes').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const assetReturn = await offboardingService.trackAssetReturn(
        employeeId,
        req.body.assets,
        req.user.id
      )
      
      logger.info(`Asset return tracked for employee: ${employeeId}`, { 
        userId: req.user.id,
        assetsCount: req.body.assets.length 
      })
      
      res.json(assetReturn)
    } catch (error) {
      logger.error('Error tracking asset return:', error)
      res.status(500).json({ error: 'Failed to track asset return' })
    }
  }
)

/**
 * @route POST /api/offboarding/:employeeId/access-revocation
 * @desc Revoke system access for departing employee
 * @access HR, IT
 */
router.post('/:employeeId/access-revocation',
  requirePermission('hr', 'it'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    body('systems').isArray().withMessage('Systems must be an array'),
    body('systems.*.systemName').notEmpty().withMessage('System name is required'),
    body('systems.*.accessType').isIn(['full', 'read_only', 'admin', 'user']),
    body('systems.*.revocationDate').optional().isISO8601(),
    body('systems.*.status').isIn(['pending', 'revoked', 'failed']),
    body('immediateRevocation').isBoolean()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const accessRevocation = await offboardingService.revokeSystemAccess(
        employeeId,
        req.body,
        req.user.id
      )
      
      logger.info(`System access revocation initiated for employee: ${employeeId}`, { 
        userId: req.user.id,
        systemsCount: req.body.systems.length 
      })
      
      res.json(accessRevocation)
    } catch (error) {
      logger.error('Error revoking system access:', error)
      res.status(500).json({ error: 'Failed to revoke system access' })
    }
  }
)

/**
 * @route GET /api/offboarding/analytics/overview
 * @desc Get offboarding analytics overview
 * @access HR Admin
 */
router.get('/analytics/overview',
  requirePermission('hr_admin'),
  [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('department').optional().isUUID(),
    query('terminationType').optional().isIn(['voluntary', 'involuntary', 'retirement', 'layoff'])
  ],
  validateRequest,
  async (req, res) => {
    try {
      const analytics = await offboardingService.getOffboardingAnalytics(req.query)
      res.json(analytics)
    } catch (error) {
      logger.error('Error fetching offboarding analytics:', error)
      res.status(500).json({ error: 'Failed to fetch offboarding analytics' })
    }
  }
)

/**
 * @route PUT /api/offboarding/:employeeId/complete
 * @desc Complete offboarding process
 * @access HR
 */
router.put('/:employeeId/complete',
  requirePermission('hr'),
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    body('finalNotes').optional().isString(),
    body('rehireEligible').isBoolean(),
    body('completionDate').optional().isISO8601()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      const completion = await offboardingService.completeOffboarding(
        employeeId,
        req.body,
        req.user.id
      )
      
      logger.info(`Offboarding completed for employee: ${employeeId}`, { 
        userId: req.user.id 
      })
      
      res.json(completion)
    } catch (error) {
      logger.error('Error completing offboarding:', error)
      res.status(500).json({ error: 'Failed to complete offboarding process' })
    }
  }
)

export default router
