{"date":"Tue Jun 24 2025 23:57:07 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2551,2339,2339,2339,2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":562598.156},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5111143,"external":8141465,"heapTotal":191696896,"heapUsed":158799544,"rss":246075392},"pid":43364,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 23:57:55 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2551,2339,2339,2339,2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":562646.171},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5111063,"external":8141385,"heapTotal":189861888,"heapUsed":161052440,"rss":243605504},"pid":13884,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 23:58:22 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2551,2339,2339,2339,2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":562673.703},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5111087,"external":8141409,"heapTotal":191959040,"heapUsed":157762416,"rss":245489664},"pid":41588,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 23:59:00 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2551,2339,2339,2339,2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":562711.64},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5111111,"external":8141433,"heapTotal":191959040,"heapUsed":162355696,"rss":248446976},"pid":32144,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Tue Jun 24 2025 23:59:13 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2551,2339,2339,2339,2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":562724.687},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5111119,"external":8141441,"heapTotal":193007616,"heapUsed":173295984,"rss":250593280},"pid":30652,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Wed Jun 25 2025 00:13:58 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2551,2339,2339,2339,2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":563609.765},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5111111,"external":8141433,"heapTotal":192483328,"heapUsed":158635248,"rss":247435264},"pid":18824,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m232\u001b[0m:\u001b[93m42\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2551: \u001b[0mProperty 'getEmployeeTeam' does not exist on type 'EmployeeService'. Did you mean 'getEmployees'?\r\n\r\n\u001b[7m232\u001b[0m       const team = await employeeService.getEmployeeTeam(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                         ~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/employeeService.ts\u001b[0m:\u001b[93m41\u001b[0m:\u001b[93m9\u001b[0m\r\n    \u001b[7m41\u001b[0m   async getEmployees(filters: any, user: any): Promise<any> {\r\n    \u001b[7m  \u001b[0m \u001b[96m        ~~~~~~~~~~~~\u001b[0m\r\n    'getEmployees' is declared here.\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m254\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getDirectReports' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m254\u001b[0m       const reports = await employeeService.getDirectReports(employeeId, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m282\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'addEmployeeSkills' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m282\u001b[0m       const skills = await employeeService.addEmployeeSkills(employeeId, req.body.skills, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m312\u001b[0m:\u001b[93m47\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'getEmployeeAnalytics' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m312\u001b[0m       const analytics = await employeeService.getEmployeeAnalytics(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                              ~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/employees.ts\u001b[0m:\u001b[93m336\u001b[0m:\u001b[93m45\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'searchEmployees' does not exist on type 'EmployeeService'.\r\n\r\n\u001b[7m336\u001b[0m       const results = await employeeService.searchEmployees(req.query, req.user)\r\n\u001b[7m   \u001b[0m \u001b[91m                                            ~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Wed Jun 25 2025 00:21:43 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: FIELD_ENCRYPTION_KEY environment variable is required\nError: FIELD_ENCRYPTION_KEY environment variable is required\n    at new FieldEncryption (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:39:13)\n    at Object.<anonymous> (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:184:32)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)","os":{"loadavg":[0,0,0],"uptime":564074.312},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5165027,"external":8195349,"heapTotal":229261312,"heapUsed":208558400,"rss":286105600},"pid":48912,"uid":null,"version":"v23.10.0"},"stack":"Error: FIELD_ENCRYPTION_KEY environment variable is required\n    at new FieldEncryption (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:39:13)\n    at Object.<anonymous> (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:184:32)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)","trace":[{"column":13,"file":"C:\\PeopleNest\\backend\\src\\utils\\encryption.ts","function":"new FieldEncryption","line":39,"method":null,"native":false},{"column":32,"file":"C:\\PeopleNest\\backend\\src\\utils\\encryption.ts","function":null,"line":184,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false}]}
{"date":"Wed Jun 25 2025 00:22:06 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: FIELD_ENCRYPTION_KEY environment variable is required\nError: FIELD_ENCRYPTION_KEY environment variable is required\n    at new FieldEncryption (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:39:13)\n    at Object.<anonymous> (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:184:32)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)","os":{"loadavg":[0,0,0],"uptime":564097.5},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5138505,"external":8168827,"heapTotal":228999168,"heapUsed":204029480,"rss":284872704},"pid":40416,"uid":null,"version":"v23.10.0"},"stack":"Error: FIELD_ENCRYPTION_KEY environment variable is required\n    at new FieldEncryption (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:39:13)\n    at Object.<anonymous> (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:184:32)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)","trace":[{"column":13,"file":"C:\\PeopleNest\\backend\\src\\utils\\encryption.ts","function":"new FieldEncryption","line":39,"method":null,"native":false},{"column":32,"file":"C:\\PeopleNest\\backend\\src\\utils\\encryption.ts","function":null,"line":184,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false}]}
{"date":"Wed Jun 25 2025 00:22:43 GMT+0100 (West Africa Standard Time)","error":{},"exception":true,"level":"error","message":"uncaughtException: FIELD_ENCRYPTION_KEY environment variable is required\nError: FIELD_ENCRYPTION_KEY environment variable is required\n    at new FieldEncryption (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:39:13)\n    at Object.<anonymous> (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:184:32)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)","os":{"loadavg":[0,0,0],"uptime":564134.281},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5138513,"external":8168835,"heapTotal":228999168,"heapUsed":203106856,"rss":284086272},"pid":39436,"uid":null,"version":"v23.10.0"},"stack":"Error: FIELD_ENCRYPTION_KEY environment variable is required\n    at new FieldEncryption (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:39:13)\n    at Object.<anonymous> (C:\\PeopleNest\\backend\\src\\utils\\encryption.ts:184:32)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)","trace":[{"column":13,"file":"C:\\PeopleNest\\backend\\src\\utils\\encryption.ts","function":"new FieldEncryption","line":39,"method":null,"native":false},{"column":32,"file":"C:\\PeopleNest\\backend\\src\\utils\\encryption.ts","function":null,"line":184,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false}]}
{"date":"Wed Jun 25 2025 00:30:10 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2339,2339,2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m152\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m152\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m197\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m197\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m401\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m401\u001b[0m         session_id: req.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                        ~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m152\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m152\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m197\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m197\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m401\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m401\u001b[0m         session_id: req.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                        ~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":564581.687},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5136260,"external":8166582,"heapTotal":222965760,"heapUsed":196955904,"rss":288907264},"pid":13888,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m152\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m152\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m197\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m197\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m401\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m401\u001b[0m         session_id: req.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                        ~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Wed Jun 25 2025 00:30:25 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2339,2339,2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m152\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m152\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m197\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m197\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m401\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m401\u001b[0m         session_id: req.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                        ~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m152\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m152\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m197\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m197\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m401\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m401\u001b[0m         session_id: req.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                        ~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":564596.515},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5136180,"external":8166502,"heapTotal":222703616,"heapUsed":195781104,"rss":284651520},"pid":47904,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m152\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m152\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m197\u001b[0m:\u001b[93m24\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m197\u001b[0m       session_id: req?.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                       ~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/utils/auditLogger.ts\u001b[0m:\u001b[93m401\u001b[0m:\u001b[93m25\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'sessionID' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'.\r\n\r\n\u001b[7m401\u001b[0m         session_id: req.sessionID,\r\n\u001b[7m   \u001b[0m \u001b[91m                        ~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Wed Jun 25 2025 00:31:11 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2307]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m8\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../middleware/security' or its corresponding type declarations.\r\n\r\n\u001b[7m13\u001b[0m } from '../middleware/security'\r\n\u001b[7m  \u001b[0m \u001b[91m       ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m8\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../middleware/security' or its corresponding type declarations.\r\n\r\n\u001b[7m13\u001b[0m } from '../middleware/security'\r\n\u001b[7m  \u001b[0m \u001b[91m       ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":564642.25},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5111367,"external":8141689,"heapTotal":220405760,"heapUsed":186526336,"rss":281710592},"pid":19072,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m8\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../middleware/security' or its corresponding type declarations.\r\n\r\n\u001b[7m13\u001b[0m } from '../middleware/security'\r\n\u001b[7m  \u001b[0m \u001b[91m       ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Wed Jun 25 2025 00:31:15 GMT+0100 (West Africa Standard Time)","error":{"diagnosticCodes":[2307,2307]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m5\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../utils/auditLogger' or its corresponding type declarations.\r\n\r\n\u001b[7m5\u001b[0m import { auditLogger } from '../utils/auditLogger'\r\n\u001b[7m \u001b[0m \u001b[91m                            ~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m8\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../middleware/security' or its corresponding type declarations.\r\n\r\n\u001b[7m13\u001b[0m } from '../middleware/security'\r\n\u001b[7m  \u001b[0m \u001b[91m       ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m5\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../utils/auditLogger' or its corresponding type declarations.\r\n\r\n\u001b[7m5\u001b[0m import { auditLogger } from '../utils/auditLogger'\r\n\u001b[7m \u001b[0m \u001b[91m                            ~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m8\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../middleware/security' or its corresponding type declarations.\r\n\r\n\u001b[7m13\u001b[0m } from '../middleware/security'\r\n\u001b[7m  \u001b[0m \u001b[91m       ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":564646.843},"process":{"argv":["C:\\PeopleNest\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\PeopleNest\\backend\\src\\server.ts"],"cwd":"C:\\PeopleNest\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5111447,"external":8141769,"heapTotal":219095040,"heapUsed":184672048,"rss":279359488},"pid":26496,"uid":null,"version":"v23.10.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m5\u001b[0m:\u001b[93m29\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../utils/auditLogger' or its corresponding type declarations.\r\n\r\n\u001b[7m5\u001b[0m import { auditLogger } from '../utils/auditLogger'\r\n\u001b[7m \u001b[0m \u001b[91m                            ~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/routes/security.ts\u001b[0m:\u001b[93m13\u001b[0m:\u001b[93m8\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2307: \u001b[0mCannot find module '../middleware/security' or its corresponding type declarations.\r\n\r\n\u001b[7m13\u001b[0m } from '../middleware/security'\r\n\u001b[7m  \u001b[0m \u001b[91m       ~~~~~~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Function._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","trace":[{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"C:\\PeopleNest\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
