import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'

export class OffboardingService {
  private db: DatabaseService

  constructor() {
    this.db = new DatabaseService()
  }

  async getOffboardingTasks(employeeId: string, user: any): Promise<any> {
    try {
      const query = `
        SELECT * FROM offboarding_tasks 
        WHERE employee_id = $1
        ORDER BY created_at DESC
      `
      const result = await this.db.query(query, [employeeId])
      return result.rows
    } catch (error) {
      logger.error('Error fetching offboarding tasks:', error)
      throw error
    }
  }

  async createOffboardingProcess(employeeId: string, data: any, user: any): Promise<any> {
    try {
      const query = `
        INSERT INTO offboarding_processes (employee_id, last_working_day, reason, created_by)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `
      const result = await this.db.query(query, [
        employeeId,
        data.lastWorkingDay,
        data.reason,
        user.id
      ])
      return result.rows[0]
    } catch (error) {
      logger.error('Error creating offboarding process:', error)
      throw error
    }
  }

  async updateOffboardingTask(taskId: string, data: any, user: any): Promise<any> {
    try {
      const query = `
        UPDATE offboarding_tasks 
        SET status = $1, completed_at = $2, notes = $3
        WHERE id = $4
        RETURNING *
      `
      const result = await this.db.query(query, [
        data.status,
        data.completedAt,
        data.notes,
        taskId
      ])
      return result.rows[0]
    } catch (error) {
      logger.error('Error updating offboarding task:', error)
      throw error
    }
  }

  async getOffboardingProcess(employeeId: string, user: any): Promise<any> {
    try {
      const query = `
        SELECT * FROM offboarding_processes 
        WHERE employee_id = $1
      `
      const result = await this.db.query(query, [employeeId])
      return result.rows[0]
    } catch (error) {
      logger.error('Error fetching offboarding process:', error)
      throw error
    }
  }
}
