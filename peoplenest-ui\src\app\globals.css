@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;
  --radius: 0.5rem;

  /* Enhanced color variables for better contrast */
  --text-primary: #171717;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --text-inverse: #ffffff;
  --surface-primary: #ffffff;
  --surface-secondary: #f8fafc;
  --surface-tertiary: #f1f5f9;
  --surface-hover: #f8fafc;
  --surface-active: #e2e8f0;
  --success: #22c55e;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --info: #3b82f6;
  --info-foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --radius: var(--radius);

  /* Enhanced semantic color mappings */
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-tertiary: var(--text-tertiary);
  --color-text-inverse: var(--text-inverse);
  --color-surface-primary: var(--surface-primary);
  --color-surface-secondary: var(--surface-secondary);
  --color-surface-tertiary: var(--surface-tertiary);
  --color-surface-hover: var(--surface-hover);
  --color-surface-active: var(--surface-active);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --card: #111111;
    --card-foreground: #ededed;
    --popover: #111111;
    --popover-foreground: #ededed;
    --primary: #60a5fa;
    --primary-foreground: #0a0a0a;
    --secondary: #1e293b;
    --secondary-foreground: #ededed;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --accent: #1e293b;
    --accent-foreground: #ededed;
    --destructive: #dc2626;
    --destructive-foreground: #ededed;
    --border: #27272a;
    --input: #27272a;
    --ring: #60a5fa;

    /* Enhanced dark mode colors for better contrast */
    --text-primary: #ededed;
    --text-secondary: #a1a1aa;
    --text-tertiary: #71717a;
    --text-inverse: #0a0a0a;
    --surface-primary: #0a0a0a;
    --surface-secondary: #111111;
    --surface-tertiary: #1a1a1a;
    --surface-hover: #1e1e1e;
    --surface-active: #27272a;
    --success: #22c55e;
    --success-foreground: #0a0a0a;
    --warning: #f59e0b;
    --warning-foreground: #0a0a0a;
    --info: #60a5fa;
    --info-foreground: #0a0a0a;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: color-mix(in srgb, var(--muted-foreground) 30%, transparent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: color-mix(in srgb, var(--muted-foreground) 50%, transparent);
}

/* Focus styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px var(--ring);
}

/* Animation keyframes */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fade-out {
  from { opacity: 1; }
  to { opacity: 0; }
}

@keyframes slide-in-from-top {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

@keyframes slide-in-from-bottom {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes slide-in-from-left {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slide-in-from-right {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes accordion-down {
  from { height: 0; }
  to { height: var(--radix-accordion-content-height); }
}

@keyframes accordion-up {
  from { height: var(--radix-accordion-content-height); }
  to { height: 0; }
}

/* Utility classes */
.animate-in {
  animation-duration: 150ms;
  animation-fill-mode: both;
}

.animate-out {
  animation-duration: 150ms;
  animation-fill-mode: both;
}

.fade-in { animation-name: fade-in; }
.fade-out { animation-name: fade-out; }
.slide-in-from-top { animation-name: slide-in-from-top; }
.slide-in-from-bottom { animation-name: slide-in-from-bottom; }
.slide-in-from-left { animation-name: slide-in-from-left; }
.slide-in-from-right { animation-name: slide-in-from-right; }

/* Text utilities */
.text-balance {
  text-wrap: balance;
}

/* Glass morphism effect */
.glass {
  background: color-mix(in srgb, var(--background) 80%, transparent);
  backdrop-filter: blur(10px);
  border: 1px solid color-mix(in srgb, var(--border) 50%, transparent);
}
