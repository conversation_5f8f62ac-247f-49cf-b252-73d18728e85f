// Theme utilities for PeopleNest HRMS
// Provides consistent color management and theme switching capabilities

export const themeColors = {
  // Light mode colors
  light: {
    background: '#ffffff',
    foreground: '#171717',
    card: '#ffffff',
    cardForeground: '#171717',
    popover: '#ffffff',
    popoverForeground: '#171717',
    primary: '#3b82f6',
    primaryForeground: '#ffffff',
    secondary: '#f1f5f9',
    secondaryForeground: '#0f172a',
    muted: '#f1f5f9',
    mutedForeground: '#64748b',
    accent: '#f1f5f9',
    accentForeground: '#0f172a',
    destructive: '#ef4444',
    destructiveForeground: '#ffffff',
    border: '#e2e8f0',
    input: '#e2e8f0',
    ring: '#3b82f6',
    
    // Enhanced semantic colors
    textPrimary: '#171717',
    textSecondary: '#64748b',
    textTertiary: '#94a3b8',
    textInverse: '#ffffff',
    surfacePrimary: '#ffffff',
    surfaceSecondary: '#f8fafc',
    surfaceTertiary: '#f1f5f9',
    surfaceHover: '#f8fafc',
    surfaceActive: '#e2e8f0',
    success: '#22c55e',
    successForeground: '#ffffff',
    warning: '#f59e0b',
    warningForeground: '#ffffff',
    info: '#3b82f6',
    infoForeground: '#ffffff',
  },
  
  // Dark mode colors
  dark: {
    background: '#0a0a0a',
    foreground: '#ededed',
    card: '#111111',
    cardForeground: '#ededed',
    popover: '#111111',
    popoverForeground: '#ededed',
    primary: '#60a5fa',
    primaryForeground: '#0a0a0a',
    secondary: '#1e293b',
    secondaryForeground: '#ededed',
    muted: '#1e293b',
    mutedForeground: '#94a3b8',
    accent: '#1e293b',
    accentForeground: '#ededed',
    destructive: '#dc2626',
    destructiveForeground: '#ededed',
    border: '#27272a',
    input: '#27272a',
    ring: '#60a5fa',
    
    // Enhanced semantic colors
    textPrimary: '#ededed',
    textSecondary: '#a1a1aa',
    textTertiary: '#71717a',
    textInverse: '#0a0a0a',
    surfacePrimary: '#0a0a0a',
    surfaceSecondary: '#111111',
    surfaceTertiary: '#1a1a1a',
    surfaceHover: '#1e1e1e',
    surfaceActive: '#27272a',
    success: '#22c55e',
    successForeground: '#0a0a0a',
    warning: '#f59e0b',
    warningForeground: '#0a0a0a',
    info: '#60a5fa',
    infoForeground: '#0a0a0a',
  }
} as const

export type ThemeMode = 'light' | 'dark' | 'system'

/**
 * Utility function to get the current theme mode
 */
export function getThemeMode(): ThemeMode {
  if (typeof window === 'undefined') return 'system'
  
  const stored = localStorage.getItem('theme-mode')
  if (stored === 'light' || stored === 'dark') return stored
  
  return 'system'
}

/**
 * Utility function to set the theme mode
 */
export function setThemeMode(mode: ThemeMode) {
  if (typeof window === 'undefined') return
  
  if (mode === 'system') {
    localStorage.removeItem('theme-mode')
    document.documentElement.classList.remove('light', 'dark')
  } else {
    localStorage.setItem('theme-mode', mode)
    document.documentElement.classList.remove('light', 'dark')
    document.documentElement.classList.add(mode)
  }
}

/**
 * Utility function to toggle between light and dark modes
 */
export function toggleTheme() {
  const current = getThemeMode()
  const next = current === 'light' ? 'dark' : 'light'
  setThemeMode(next)
}

/**
 * Hook to detect system theme preference
 */
export function useSystemTheme() {
  if (typeof window === 'undefined') return 'light'
  
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
}

/**
 * Utility to get contrast-safe text color for a given background
 */
export function getContrastTextColor(backgroundColor: string, theme: 'light' | 'dark' = 'light') {
  // Simple heuristic - in a real app you might want to use a proper contrast calculation
  const colors = themeColors[theme]
  
  // For dark backgrounds, use light text
  if (backgroundColor.includes('dark') || backgroundColor.includes('black') || 
      backgroundColor === colors.primary || backgroundColor === colors.destructive) {
    return colors.textInverse
  }
  
  // For light backgrounds, use dark text
  return colors.textPrimary
}

/**
 * Status color mappings for consistent UI
 */
export const statusColors = {
  active: {
    light: 'bg-green-500/10 text-green-600',
    dark: 'bg-green-500/10 text-green-400'
  },
  inactive: {
    light: 'bg-muted text-muted-foreground',
    dark: 'bg-muted text-muted-foreground'
  },
  pending: {
    light: 'bg-yellow-500/10 text-yellow-600',
    dark: 'bg-yellow-500/10 text-yellow-400'
  },
  approved: {
    light: 'bg-green-500/10 text-green-600',
    dark: 'bg-green-500/10 text-green-400'
  },
  rejected: {
    light: 'bg-red-500/10 text-red-600',
    dark: 'bg-red-500/10 text-red-400'
  },
  draft: {
    light: 'bg-muted text-muted-foreground',
    dark: 'bg-muted text-muted-foreground'
  }
} as const

export type StatusType = keyof typeof statusColors

/**
 * Get status color classes for current theme
 */
export function getStatusColor(status: StatusType, theme: 'light' | 'dark' = 'light') {
  return statusColors[status][theme]
}
