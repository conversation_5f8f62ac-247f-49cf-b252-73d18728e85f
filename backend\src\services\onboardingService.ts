import { v4 as uuidv4 } from 'uuid'
import { addDays, differenceInDays, format } from 'date-fns'
import { DatabaseService } from './databaseService'
import { NotificationService } from './notificationService'
import { EmailService } from './emailService'
import { logger } from '../utils/logger'

export interface OnboardingTemplate {
  id: string
  name: string
  description?: string
  tasks: OnboardingTask[]
  estimatedDuration: number // in days
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

export interface OnboardingTask {
  id: string
  title: string
  description?: string
  category: 'documentation' | 'equipment' | 'training' | 'meetings' | 'system_access'
  required: boolean
  estimatedHours?: number
  dependencies: string[] // task IDs
  assignedTo?: string // user ID
  dueDate?: Date
  status: 'pending' | 'in_progress' | 'completed' | 'skipped'
  completedAt?: Date
  completedBy?: string
  notes?: string
  attachments: string[]
  timeSpent?: number
}

export interface OnboardingProcess {
  id: string
  employeeId: string
  templateId: string
  status: 'not_started' | 'in_progress' | 'completed' | 'extended'
  startDate: Date
  expectedEndDate: Date
  actualEndDate?: Date
  progressPercentage: number
  tasks: OnboardingTask[]
  buddyId?: string
  notes?: string
  createdAt: Date
  updatedAt: Date
  createdBy: string
}

export class OnboardingService {
  private db: DatabaseService
  private notificationService: NotificationService
  private emailService: EmailService

  constructor() {
    this.db = new DatabaseService()
    this.notificationService = new NotificationService()
    this.emailService = new EmailService()
  }

  /**
   * Create a new onboarding template
   */
  async createTemplate(templateData: Partial<OnboardingTemplate>, createdBy: string): Promise<OnboardingTemplate> {
    const template: OnboardingTemplate = {
      id: uuidv4(),
      name: templateData.name!,
      description: templateData.description,
      tasks: templateData.tasks?.map(task => ({
        ...task,
        id: uuidv4(),
        status: 'pending',
        attachments: []
      })) || [],
      estimatedDuration: this.calculateEstimatedDuration(templateData.tasks || []),
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy
    }

    await this.db.query(
      `INSERT INTO onboarding_templates (id, name, description, tasks, estimated_duration, is_active, created_by)
       VALUES ($1, $2, $3, $4, $5, $6, $7)`,
      [template.id, template.name, template.description, JSON.stringify(template.tasks), 
       template.estimatedDuration, template.isActive, template.createdBy]
    )

    logger.info(`Onboarding template created: ${template.id}`)
    return template
  }

  /**
   * Get all active onboarding templates
   */
  async getTemplates(): Promise<OnboardingTemplate[]> {
    const result = await this.db.query(
      `SELECT * FROM onboarding_templates WHERE is_active = true ORDER BY created_at DESC`
    )

    return result.rows.map(row => ({
      ...row,
      tasks: JSON.parse(row.tasks),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    }))
  }

  /**
   * Start onboarding process for an employee
   */
  async startOnboarding(
    employeeId: string, 
    data: {
      templateId: string
      startDate: string
      buddyId?: string
      customTasks?: Partial<OnboardingTask>[]
      notes?: string
    },
    createdBy: string
  ): Promise<OnboardingProcess> {
    // Get template
    const template = await this.getTemplateById(data.templateId)
    if (!template) {
      throw new Error('Onboarding template not found')
    }

    // Check if employee already has active onboarding
    const existingOnboarding = await this.getActiveOnboarding(employeeId)
    if (existingOnboarding) {
      throw new Error('Employee already has an active onboarding process')
    }

    const startDate = new Date(data.startDate)
    const expectedEndDate = addDays(startDate, template.estimatedDuration)

    // Prepare tasks with due dates
    const tasks = template.tasks.map(task => ({
      ...task,
      id: uuidv4(),
      status: 'pending' as const,
      dueDate: this.calculateTaskDueDate(startDate, task, template.tasks),
      attachments: []
    }))

    // Add custom tasks if provided
    if (data.customTasks) {
      const customTasks = data.customTasks.map(task => ({
        id: uuidv4(),
        title: task.title!,
        description: task.description,
        category: task.category || 'documentation' as const,
        required: task.required || false,
        estimatedHours: task.estimatedHours,
        dependencies: task.dependencies || [],
        status: 'pending' as const,
        dueDate: addDays(startDate, 7), // Default to 7 days from start
        attachments: []
      }))
      tasks.push(...customTasks)
    }

    const onboarding: OnboardingProcess = {
      id: uuidv4(),
      employeeId,
      templateId: data.templateId,
      status: 'in_progress',
      startDate,
      expectedEndDate,
      progressPercentage: 0,
      tasks,
      buddyId: data.buddyId,
      notes: data.notes,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy
    }

    // Save to database
    await this.db.query(
      `INSERT INTO onboarding_processes 
       (id, employee_id, template_id, status, start_date, expected_end_date, 
        progress_percentage, tasks, buddy_id, notes, created_by)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)`,
      [onboarding.id, onboarding.employeeId, onboarding.templateId, onboarding.status,
       onboarding.startDate, onboarding.expectedEndDate, onboarding.progressPercentage,
       JSON.stringify(onboarding.tasks), onboarding.buddyId, onboarding.notes, onboarding.createdBy]
    )

    // Send notifications
    await this.sendOnboardingNotifications(onboarding)

    logger.info(`Onboarding process started for employee: ${employeeId}`)
    return onboarding
  }

  /**
   * Get onboarding status for an employee
   */
  async getOnboardingStatus(employeeId: string): Promise<OnboardingProcess | null> {
    const result = await this.db.query(
      `SELECT op.*, ot.name as template_name, e.first_name, e.last_name, e.email
       FROM onboarding_processes op
       JOIN onboarding_templates ot ON op.template_id = ot.id
       JOIN employees e ON op.employee_id = e.id
       WHERE op.employee_id = $1 AND op.status != 'completed'
       ORDER BY op.created_at DESC
       LIMIT 1`,
      [employeeId]
    )

    if (result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      ...row,
      tasks: JSON.parse(row.tasks),
      startDate: new Date(row.start_date),
      expectedEndDate: new Date(row.expected_end_date),
      actualEndDate: row.actual_end_date ? new Date(row.actual_end_date) : undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    }
  }

  /**
   * Complete an onboarding task
   */
  async completeTask(
    taskId: string,
    data: {
      notes?: string
      attachments?: string[]
      timeSpent?: number
    },
    completedBy: string
  ): Promise<OnboardingTask> {
    // Get the onboarding process containing this task
    const onboarding = await this.getOnboardingByTaskId(taskId)
    if (!onboarding) {
      throw new Error('Onboarding process not found for this task')
    }

    // Update the task
    const updatedTasks = onboarding.tasks.map(task => {
      if (task.id === taskId) {
        return {
          ...task,
          status: 'completed' as const,
          completedAt: new Date(),
          completedBy,
          notes: data.notes,
          attachments: data.attachments || task.attachments,
          timeSpent: data.timeSpent
        }
      }
      return task
    })

    // Calculate new progress
    const completedTasks = updatedTasks.filter(task => task.status === 'completed').length
    const progressPercentage = Math.round((completedTasks / updatedTasks.length) * 100)

    // Update onboarding process
    await this.db.query(
      `UPDATE onboarding_processes 
       SET tasks = $1, progress_percentage = $2, updated_at = NOW()
       WHERE id = $3`,
      [JSON.stringify(updatedTasks), progressPercentage, onboarding.id]
    )

    // Check if onboarding is complete
    if (progressPercentage === 100) {
      await this.completeOnboarding(onboarding.id)
    }

    const completedTask = updatedTasks.find(task => task.id === taskId)!
    logger.info(`Onboarding task completed: ${taskId}`)
    
    return completedTask
  }

  /**
   * Get onboarding analytics
   */
  async getOnboardingAnalytics(filters: any): Promise<any> {
    // Implementation for analytics
    const analytics = {
      totalOnboardings: 0,
      completedOnboardings: 0,
      averageCompletionTime: 0,
      completionRate: 0,
      departmentBreakdown: [],
      taskCompletionRates: [],
      bottlenecks: []
    }

    // Add analytics queries here
    return analytics
  }

  /**
   * Extend onboarding period
   */
  async extendOnboarding(
    employeeId: string,
    data: {
      newEndDate: string
      reason: string
      additionalTasks?: Partial<OnboardingTask>[]
    },
    extendedBy: string
  ): Promise<OnboardingProcess> {
    const onboarding = await this.getOnboardingStatus(employeeId)
    if (!onboarding) {
      throw new Error('Active onboarding process not found')
    }

    const newEndDate = new Date(data.newEndDate)
    let updatedTasks = [...onboarding.tasks]

    // Add additional tasks if provided
    if (data.additionalTasks) {
      const additionalTasks = data.additionalTasks.map(task => ({
        id: uuidv4(),
        title: task.title!,
        description: task.description,
        category: task.category || 'documentation' as const,
        required: task.required || false,
        estimatedHours: task.estimatedHours,
        dependencies: task.dependencies || [],
        status: 'pending' as const,
        attachments: []
      }))
      updatedTasks.push(...additionalTasks)
    }

    // Update onboarding process
    await this.db.query(
      `UPDATE onboarding_processes 
       SET expected_end_date = $1, tasks = $2, status = 'extended', updated_at = NOW()
       WHERE id = $3`,
      [newEndDate, JSON.stringify(updatedTasks), onboarding.id]
    )

    // Log extension
    await this.db.query(
      `INSERT INTO onboarding_extensions (onboarding_id, previous_end_date, new_end_date, reason, extended_by)
       VALUES ($1, $2, $3, $4, $5)`,
      [onboarding.id, onboarding.expectedEndDate, newEndDate, data.reason, extendedBy]
    )

    logger.info(`Onboarding extended for employee: ${employeeId}`)
    return { ...onboarding, expectedEndDate: newEndDate, tasks: updatedTasks }
  }

  // Private helper methods
  private calculateEstimatedDuration(tasks: Partial<OnboardingTask>[]): number {
    const totalHours = tasks.reduce((sum, task) => sum + (task.estimatedHours || 2), 0)
    return Math.ceil(totalHours / 8) // Convert to days
  }

  private calculateTaskDueDate(startDate: Date, task: Partial<OnboardingTask>, allTasks: Partial<OnboardingTask>[]): Date {
    // Simple calculation - can be enhanced with dependency logic
    const taskIndex = allTasks.indexOf(task)
    return addDays(startDate, Math.floor(taskIndex / 2) + 1)
  }

  private async getTemplateById(templateId: string): Promise<OnboardingTemplate | null> {
    const result = await this.db.query(
      `SELECT * FROM onboarding_templates WHERE id = $1 AND is_active = true`,
      [templateId]
    )

    if (result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      ...row,
      tasks: JSON.parse(row.tasks),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    }
  }

  private async getActiveOnboarding(employeeId: string): Promise<OnboardingProcess | null> {
    const result = await this.db.query(
      `SELECT * FROM onboarding_processes 
       WHERE employee_id = $1 AND status IN ('not_started', 'in_progress', 'extended')`,
      [employeeId]
    )

    return result.rows.length > 0 ? result.rows[0] : null
  }

  private async getOnboardingByTaskId(taskId: string): Promise<OnboardingProcess | null> {
    const result = await this.db.query(
      `SELECT * FROM onboarding_processes 
       WHERE tasks::text LIKE '%"id":"${taskId}"%'`
    )

    if (result.rows.length === 0) {
      return null
    }

    const row = result.rows[0]
    return {
      ...row,
      tasks: JSON.parse(row.tasks),
      startDate: new Date(row.start_date),
      expectedEndDate: new Date(row.expected_end_date),
      actualEndDate: row.actual_end_date ? new Date(row.actual_end_date) : undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    }
  }

  private async completeOnboarding(onboardingId: string): Promise<void> {
    await this.db.query(
      `UPDATE onboarding_processes 
       SET status = 'completed', actual_end_date = NOW(), updated_at = NOW()
       WHERE id = $1`,
      [onboardingId]
    )

    logger.info(`Onboarding process completed: ${onboardingId}`)
  }

  private async sendOnboardingNotifications(onboarding: OnboardingProcess): Promise<void> {
    // Send notifications to employee, buddy, manager, HR
    // Implementation depends on notification service
    logger.info(`Onboarding notifications sent for: ${onboarding.employeeId}`)
  }
}
