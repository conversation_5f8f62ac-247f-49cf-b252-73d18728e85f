import { Request, Response, NextFunction } from 'express'
import rateLimit from 'express-rate-limit'
import helmet from 'helmet'
import { auditLogger } from '../utils/auditLogger'
import { fieldEncryption } from '../utils/encryption'
import { logger } from '../utils/logger'

// Extend session interface
declare module 'express-session' {
  interface SessionData {
    lastActivity?: number
  }
}

// Rate limiting configurations
export const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: {
    error: 'Too many authentication attempts, please try again later',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    logger.warn('Authentication rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    })
    
    res.status(429).json({
      error: 'Too many authentication attempts, please try again later',
      code: 'RATE_LIMIT_EXCEEDED'
    })
  }
})

export const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per window
  message: {
    error: 'Too many API requests, please try again later',
    code: 'API_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false
})

export const sensitiveDataRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 sensitive data requests per hour
  message: {
    error: 'Too many sensitive data requests, please try again later',
    code: 'SENSITIVE_DATA_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false
})

/**
 * Security headers middleware
 */
export const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", process.env.FRONTEND_URL || "http://localhost:3000"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: "strict-origin-when-cross-origin" }
})

/**
 * Request sanitization middleware
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // Sanitize request body
  if (req.body) {
    req.body = sanitizeObject(req.body)
  }

  // Sanitize query parameters
  if (req.query) {
    req.query = sanitizeObject(req.query)
  }

  // Sanitize URL parameters
  if (req.params) {
    req.params = sanitizeObject(req.params)
  }

  next()
}

/**
 * Data classification middleware
 */
export const classifyData = (classification: 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED') => {
  return (req: Request, res: Response, next: NextFunction) => {
    req.dataClassification = classification
    
    // Log access to classified data
    if (classification === 'CONFIDENTIAL' || classification === 'RESTRICTED') {
      logger.info('Classified data access', {
        classification,
        path: req.path,
        method: req.method,
        userId: req.user?.id,
        ip: req.ip
      })
    }

    next()
  }
}

/**
 * PII detection and protection middleware
 */
export const protectPII = (req: Request, res: Response, next: NextFunction) => {
  // Store original json method
  const originalJson = res.json

  // Override json method to encrypt PII fields
  res.json = function(data: any) {
    if (data && typeof data === 'object') {
      // Check if response contains PII fields
      const protectedData = protectPIIInResponse(data)
      return originalJson.call(this, protectedData)
    }
    return originalJson.call(this, data)
  }

  next()
}

/**
 * Audit logging middleware for sensitive operations
 */
export const auditSensitiveOperation = (operation: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const startTime = Date.now()

    // Store original end method
    const originalEnd = res.end

    // Override end method to log after response
    res.end = function(chunk?: any, encoding?: any, cb?: () => void) {
      const duration = Date.now() - startTime
      const statusCode = res.statusCode

      // Log sensitive operation
      if (req.user) {
        auditLogger.logAuditEvent({
          table_name: 'sensitive_operations',
          record_id: req.params.id || 'unknown',
          action: operation as any,
          new_values: {
            operation,
            path: req.path,
            method: req.method,
            statusCode,
            duration,
            body: sanitizeForLogging(req.body)
          },
          user_id: req.user.id,
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          session_id: req.sessionID,
          data_classification: req.dataClassification || 'INTERNAL'
        }).catch(error => {
          logger.error('Failed to log sensitive operation:', error)
        })
      }

      // Call original end method
      return originalEnd.call(this, chunk, encoding, cb)
    }

    next()
  }
}

/**
 * IP whitelist middleware for admin operations
 */
export const ipWhitelist = (allowedIPs: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIP = req.ip || req.connection.remoteAddress

    if (!allowedIPs.includes(clientIP)) {
      logger.warn('Unauthorized IP access attempt', {
        ip: clientIP,
        path: req.path,
        userAgent: req.get('User-Agent')
      })

      return res.status(403).json({
        error: 'Access denied from this IP address',
        code: 'IP_NOT_ALLOWED'
      })
    }

    next()
  }
}

/**
 * Session security middleware
 */
export const sessionSecurity = (req: Request, res: Response, next: NextFunction) => {
  // Check session timeout
  if (req.session && req.session.lastActivity) {
    const sessionTimeout = 8 * 60 * 60 * 1000 // 8 hours
    const timeSinceLastActivity = Date.now() - req.session.lastActivity

    if (timeSinceLastActivity > sessionTimeout) {
      req.session.destroy((err) => {
        if (err) {
          logger.error('Failed to destroy expired session:', err)
        }
      })

      return res.status(401).json({
        error: 'Session expired',
        code: 'SESSION_EXPIRED'
      })
    }

    // Update last activity
    req.session.lastActivity = Date.now()
  }

  next()
}

/**
 * Suspicious activity detection middleware
 */
export const detectSuspiciousActivity = (req: Request, res: Response, next: NextFunction) => {
  if (!req.user) {
    return next()
  }

  const suspiciousPatterns = [
    // Multiple rapid requests
    { pattern: 'rapid_requests', threshold: 10, window: 60000 }, // 10 requests in 1 minute
    // Unusual access patterns
    { pattern: 'unusual_hours', threshold: 1, window: 3600000 }, // Access outside business hours
    // Multiple failed operations
    { pattern: 'failed_operations', threshold: 5, window: 300000 } // 5 failures in 5 minutes
  ]

  // This would be implemented with a more sophisticated detection system
  // For now, just log the request for monitoring
  logger.debug('Request monitoring', {
    userId: req.user.id,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date()
  })

  next()
}

/**
 * Data loss prevention middleware
 */
export const dataLossPrevention = (req: Request, res: Response, next: NextFunction) => {
  // Check for potential data exfiltration patterns
  if (req.method === 'GET' && req.query.limit) {
    const limit = parseInt(req.query.limit as string)
    const maxAllowedLimit = 1000

    if (limit > maxAllowedLimit) {
      logger.warn('Potential data exfiltration attempt', {
        userId: req.user?.id,
        requestedLimit: limit,
        maxAllowed: maxAllowedLimit,
        path: req.path,
        ip: req.ip
      })

      return res.status(400).json({
        error: `Requested limit (${limit}) exceeds maximum allowed (${maxAllowedLimit})`,
        code: 'LIMIT_EXCEEDED'
      })
    }
  }

  next()
}

// Helper functions

function sanitizeObject(obj: any): any {
  if (typeof obj !== 'object' || obj === null) {
    return sanitizeString(obj)
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item))
  }

  const sanitized: any = {}
  for (const [key, value] of Object.entries(obj)) {
    sanitized[key] = sanitizeObject(value)
  }

  return sanitized
}

function sanitizeString(str: any): any {
  if (typeof str !== 'string') {
    return str
  }

  // Remove potential XSS patterns
  return str
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim()
}

function protectPIIInResponse(data: any): any {
  if (!data || typeof data !== 'object') {
    return data
  }

  if (Array.isArray(data)) {
    return data.map(item => protectPIIInResponse(item))
  }

  const protectedData = { ...data }

  // Mask PII fields in response
  const piiFields = ['national_id', 'bank_account', 'phone', 'personal_email']

  for (const field of piiFields) {
    if (protectedData[field]) {
      protectedData[field] = maskPIIField(protectedData[field], field)
    }
  }

  return protectedData
}

function maskPIIField(value: string, fieldType: string): string {
  if (!value) return value

  switch (fieldType) {
    case 'national_id':
      return value.replace(/\d(?=\d{4})/g, '*')
    case 'bank_account':
      return value.replace(/\d(?=\d{4})/g, '*')
    case 'phone':
      return value.replace(/\d(?=\d{4})/g, '*')
    case 'personal_email':
      const [local, domain] = value.split('@')
      return `${local.charAt(0)}***@${domain}`
    default:
      return value.replace(/./g, '*')
  }
}

function sanitizeForLogging(data: any): any {
  if (!data || typeof data !== 'object') {
    return data
  }

  const sanitized = { ...data }
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'national_id', 'bank_account']

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]'
    }
  }

  return sanitized
}

// Extend Express Request interface
declare global {
  namespace Express {
    interface Request {
      dataClassification?: 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED'
    }
  }
}

export default {
  authRateLimit,
  apiRateLimit,
  sensitiveDataRateLimit,
  securityHeaders,
  sanitizeInput,
  classifyData,
  protectPII,
  auditSensitiveOperation,
  ipWhitelist,
  sessionSecurity,
  detectSuspiciousActivity,
  dataLossPrevention
}
