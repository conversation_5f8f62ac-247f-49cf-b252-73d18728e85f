"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Building,
  Users,
  Edit,
  Download,
  Share,
  MoreHorizontal,
  Star,
  Award,
  Target,
  TrendingUp,
  Clock,
  DollarSign,
  FileText,
  MessageSquare,
  Settings,
  Shield,
  AlertCircle,
  CheckCircle,
  Loader2
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { employeeApi, type Employee, handleApiResponse } from "@/lib/api/employeeApi"
import { toast } from "sonner"

interface EmployeeProfileProps {
  employeeId: string
  onEdit?: () => void
  onClose?: () => void
}

interface EmployeeProfileData extends Employee {
  skills: Array<{
    id: string
    name: string
    level: number
    category: string
  }>
  recentReviews: Array<{
    id: string
    reviewDate: string
    rating: number
    reviewer: string
    comments: string
  }>
  activeGoals: Array<{
    id: string
    title: string
    description: string
    progress: number
    dueDate: string
    status: string
  }>
}

export function EmployeeProfile({ employeeId, onEdit, onClose }: EmployeeProfileProps) {
  const [employee, setEmployee] = useState<EmployeeProfileData | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    loadEmployeeProfile()
  }, [employeeId])

  const loadEmployeeProfile = async () => {
    setLoading(true)
    try {
      const response = await employeeApi.getEmployeeProfile(employeeId)
      const profileData = handleApiResponse(response, undefined, true)
      if (profileData) {
        setEmployee(profileData as EmployeeProfileData)
      }
    } catch (error) {
      console.error("Failed to load employee profile:", error)
      toast.error("Failed to load employee profile")
    } finally {
      setLoading(false)
    }
  }

  const handleExportProfile = async () => {
    try {
      // Implementation for exporting employee profile
      toast.success("Profile exported successfully")
    } catch (error) {
      toast.error("Failed to export profile")
    }
  }

  const handleShareProfile = async () => {
    try {
      // Implementation for sharing employee profile
      toast.success("Profile link copied to clipboard")
    } catch (error) {
      toast.error("Failed to share profile")
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!employee) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">Employee Not Found</h3>
          <p className="text-muted-foreground">The requested employee profile could not be loaded.</p>
        </div>
      </div>
    )
  }

  const fullName = `${employee.firstName} ${employee.lastName}`
  const averageRating = employee.recentReviews.length > 0 
    ? employee.recentReviews.reduce((sum, review) => sum + review.rating, 0) / employee.recentReviews.length 
    : 0

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-6xl mx-auto space-y-6"
    >
      {/* Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={employee.avatar} alt={fullName} />
                <AvatarFallback className="text-lg">
                  {employee.firstName[0]}{employee.lastName[0]}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-2">
                <div>
                  <h1 className="text-2xl font-bold text-foreground">{fullName}</h1>
                  <p className="text-lg text-muted-foreground">{employee.positionId}</p>
                  <p className="text-sm text-muted-foreground">{employee.departmentId}</p>
                </div>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Mail className="h-4 w-4" />
                    <span>{employee.email}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Phone className="h-4 w-4" />
                    <span>{employee.phone}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {new Date(employee.hireDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={employee.status === 'active' ? 'default' : 'secondary'}>
                    {employee.status}
                  </Badge>
                  <Badge variant="outline">{employee.employeeType.replace('_', ' ')}</Badge>
                  <Badge variant="outline">{employee.workLocation}</Badge>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleShareProfile}>
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" size="sm" onClick={handleExportProfile}>
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              {onEdit && (
                <Button size="sm" onClick={onEdit}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Star className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm text-muted-foreground">Performance</p>
                <p className="text-lg font-semibold">{averageRating.toFixed(1)}/5.0</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-blue-500 dark:text-blue-400" />
              <div>
                <p className="text-sm text-muted-foreground">Active Goals</p>
                <p className="text-lg font-semibold">{employee.activeGoals.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-green-500 dark:text-green-400" />
              <div>
                <p className="text-sm text-muted-foreground">Skills</p>
                <p className="text-lg font-semibold">{employee.skills?.length || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-purple-500 dark:text-purple-400" />
              <div>
                <p className="text-sm text-muted-foreground">Tenure</p>
                <p className="text-lg font-semibold">
                  {Math.floor((Date.now() - new Date(employee.hireDate).getTime()) / (1000 * 60 * 60 * 24 * 365))} years
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="skills">Skills</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Personal Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Date of Birth</p>
                    <p className="font-medium">
                      {employee.dateOfBirth ? new Date(employee.dateOfBirth).toLocaleDateString() : 'Not provided'}
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Gender</p>
                    <p className="font-medium">{employee.gender || 'Not specified'}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Personal Email</p>
                    <p className="font-medium">{employee.personalEmail || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Phone</p>
                    <p className="font-medium">{employee.phone}</p>
                  </div>
                </div>
                {employee.address && (
                  <div>
                    <p className="text-muted-foreground text-sm">Address</p>
                    <p className="font-medium">
                      {[
                        employee.address.street,
                        employee.address.city,
                        employee.address.state,
                        employee.address.zipCode,
                        employee.address.country
                      ].filter(Boolean).join(', ') || 'Not provided'}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Employment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5" />
                  Employment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Employee ID</p>
                    <p className="font-medium">{employee.id}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Hire Date</p>
                    <p className="font-medium">{new Date(employee.hireDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Employment Type</p>
                    <p className="font-medium">{employee.employeeType.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Work Location</p>
                    <p className="font-medium">{employee.workLocation}</p>
                  </div>
                  {employee.salary && (
                    <div>
                      <p className="text-muted-foreground">Salary</p>
                      <p className="font-medium">{employee.currency} {employee.salary.toLocaleString()}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-muted-foreground">Manager</p>
                    <p className="font-medium">{employee.managerId || 'Not assigned'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Emergency Contact */}
          {employee.emergencyContact && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Emergency Contact
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Name</p>
                    <p className="font-medium">{employee.emergencyContact.name || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Relationship</p>
                    <p className="font-medium">{employee.emergencyContact.relationship || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Phone</p>
                    <p className="font-medium">{employee.emergencyContact.phone || 'Not provided'}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Email</p>
                    <p className="font-medium">{employee.emergencyContact.email || 'Not provided'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="skills" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Skills & Competencies</CardTitle>
              <CardDescription>Employee skills and proficiency levels</CardDescription>
            </CardHeader>
            <CardContent>
              {employee.skills && employee.skills.length > 0 ? (
                <div className="space-y-4">
                  {employee.skills.map((skill) => (
                    <div key={skill.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{skill.name}</span>
                        <Badge variant="outline">{skill.category}</Badge>
                      </div>
                      <Progress value={skill.level * 20} className="h-2" />
                      <p className="text-xs text-muted-foreground">Level {skill.level}/5</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-8">No skills recorded yet.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Performance Reviews</CardTitle>
              <CardDescription>Recent performance evaluations and feedback</CardDescription>
            </CardHeader>
            <CardContent>
              {employee.recentReviews && employee.recentReviews.length > 0 ? (
                <div className="space-y-4">
                  {employee.recentReviews.map((review) => (
                    <div key={review.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">Review by {review.reviewer}</span>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating ? 'text-yellow-500 fill-current' : 'text-muted-foreground'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {new Date(review.reviewDate).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-foreground">{review.comments}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-8">No performance reviews yet.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="goals" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Active Goals</CardTitle>
              <CardDescription>Current objectives and progress tracking</CardDescription>
            </CardHeader>
            <CardContent>
              {employee.activeGoals && employee.activeGoals.length > 0 ? (
                <div className="space-y-4">
                  {employee.activeGoals.map((goal) => (
                    <div key={goal.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{goal.title}</h4>
                        <Badge variant={goal.status === 'on_track' ? 'default' : 'secondary'}>
                          {goal.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      <p className="text-muted-foreground text-sm mb-3">{goal.description}</p>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span>Progress</span>
                          <span>{goal.progress}%</span>
                        </div>
                        <Progress value={goal.progress} className="h-2" />
                        <p className="text-xs text-muted-foreground">
                          Due: {new Date(goal.dueDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-8">No active goals set.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Documents & Certifications</CardTitle>
              <CardDescription>Employee documents and certifications</CardDescription>
            </CardHeader>
            <CardContent>
              {employee.certifications && employee.certifications.length > 0 ? (
                <div className="space-y-4">
                  {employee.certifications.map((cert, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{cert.name}</h4>
                        <Badge variant="outline">{cert.issuer}</Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                        {cert.issueDate && (
                          <div>
                            <span className="text-muted-foreground">Issued:</span> {new Date(cert.issueDate).toLocaleDateString()}
                          </div>
                        )}
                        {cert.expiryDate && (
                          <div>
                            <span className="text-muted-foreground">Expires:</span> {new Date(cert.expiryDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-center py-8">No certifications recorded.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </motion.div>
  )
}
