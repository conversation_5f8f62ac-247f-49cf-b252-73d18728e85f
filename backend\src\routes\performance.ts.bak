import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { PerformanceService } from '../services/performanceService'
import { requirePermission } from '../middleware/permissions'
import { logger } from '../utils/logger'

const router = express.Router()
const performanceService = new PerformanceService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    })
  }
  next()
}

/**
 * @route POST /api/performance/goals
 * @desc Create performance goal
 * @access HR, Manager, Employee (own goals)
 */
router.post('/goals',
  [
    body('employeeId').isUUID().withMessage('Employee ID is required'),
    body('title').notEmpty().withMessage('Goal title is required'),
    body('description').optional().isString(),
    body('category').isIn(['performance', 'development', 'behavioral', 'project', 'other']),
    body('priority').isIn(['low', 'medium', 'high', 'critical']),
    body('targetDate').isISO8601().withMessage('Valid target date is required'),
    body('measurable').isBoolean(),
    body('metrics').optional().isArray(),
    body('weight').optional().isNumeric().isFloat({ min: 0, max: 100 }),
    body('parentGoalId').optional().isUUID()
  ],
  validateRequest,
  async (req, res) => {
    try {
      // Check permissions
      if (!req.user.permissions.includes('hr') && 
          !req.user.permissions.includes('manager') && 
          req.user.employeeId !== req.body.employeeId) {
        return res.status(403).json({ error: 'Access denied' })
      }

      const goal = await performanceService.createGoal(req.body, req.user.id)
      
      logger.info(`Performance goal created: ${goal.id}`, { 
        userId: req.user.id,
        employeeId: req.body.employeeId 
      })
      
      res.status(201).json(goal)
    } catch (error) {
      logger.error('Error creating performance goal:', error)
      res.status(500).json({ error: 'Failed to create performance goal' })
    }
  }
)

/**
 * @route GET /api/performance/goals
 * @desc Get performance goals
 * @access HR, Manager, Employee (own goals)
 */
router.get('/goals',
  [
    query('employeeId').optional().isUUID(),
    query('status').optional().isIn(['draft', 'active', 'completed', 'cancelled']),
    query('category').optional().isIn(['performance', 'development', 'behavioral', 'project', 'other']),
    query('priority').optional().isIn(['low', 'medium', 'high', 'critical']),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  validateRequest,
  async (req, res) => {
    try {
      // Apply permission filters
      let filters = { ...req.query }
      
      if (!req.user.permissions.includes('hr') && !req.user.permissions.includes('manager')) {
        filters.employeeId = req.user.employeeId
      }

      const goals = await performanceService.getGoals(filters, req.user)
      res.json(goals)
    } catch (error) {
      logger.error('Error fetching performance goals:', error)
      res.status(500).json({ error: 'Failed to fetch performance goals' })
    }
  }
)

/**
 * @route PUT /api/performance/goals/:goalId
 * @desc Update performance goal
 * @access HR, Manager, Goal Owner
 */
router.put('/goals/:goalId',
  [
    param('goalId').isUUID().withMessage('Invalid goal ID'),
    body('title').optional().notEmpty(),
    body('description').optional().isString(),
    body('status').optional().isIn(['draft', 'active', 'completed', 'cancelled']),
    body('priority').optional().isIn(['low', 'medium', 'high', 'critical']),
    body('targetDate').optional().isISO8601(),
    body('progress').optional().isNumeric().isFloat({ min: 0, max: 100 }),
    body('metrics').optional().isArray(),
    body('weight').optional().isNumeric().isFloat({ min: 0, max: 100 })
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { goalId } = req.params
      const goal = await performanceService.updateGoal(goalId, req.body, req.user)
      
      if (!goal) {
        return res.status(404).json({ error: 'Goal not found or access denied' })
      }

      logger.info(`Performance goal updated: ${goalId}`, { userId: req.user.id })
      res.json(goal)
    } catch (error) {
      logger.error('Error updating performance goal:', error)
      res.status(500).json({ error: 'Failed to update performance goal' })
    }
  }
)

/**
 * @route POST /api/performance/reviews
 * @desc Create performance review
 * @access HR, Manager
 */
router.post('/reviews',
  requirePermission('hr', 'manager'),
  [
    body('employeeId').isUUID().withMessage('Employee ID is required'),
    body('reviewerId').isUUID().withMessage('Reviewer ID is required'),
    body('reviewType').isIn(['annual', 'quarterly', 'probation', 'project', 'ad_hoc']),
    body('reviewPeriodStart').isISO8601().withMessage('Valid review period start is required'),
    body('reviewPeriodEnd').isISO8601().withMessage('Valid review period end is required'),
    body('dueDate').isISO8601().withMessage('Valid due date is required'),
    body('template').optional().isObject(),
    body('goals').optional().isArray(),
    body('competencies').optional().isArray()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const review = await performanceService.createReview(req.body, req.user.id)
      
      logger.info(`Performance review created: ${review.id}`, { 
        userId: req.user.id,
        employeeId: req.body.employeeId 
      })
      
      res.status(201).json(review)
    } catch (error) {
      logger.error('Error creating performance review:', error)
      res.status(500).json({ error: 'Failed to create performance review' })
    }
  }
)

/**
 * @route GET /api/performance/reviews
 * @desc Get performance reviews
 * @access HR, Manager, Employee (own reviews)
 */
router.get('/reviews',
  [
    query('employeeId').optional().isUUID(),
    query('reviewerId').optional().isUUID(),
    query('status').optional().isIn(['draft', 'in_progress', 'completed', 'overdue']),
    query('reviewType').optional().isIn(['annual', 'quarterly', 'probation', 'project', 'ad_hoc']),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 })
  ],
  validateRequest,
  async (req, res) => {
    try {
      // Apply permission filters
      let filters = { ...req.query }
      
      if (!req.user.permissions.includes('hr') && !req.user.permissions.includes('manager')) {
        filters.employeeId = req.user.employeeId
      }

      const reviews = await performanceService.getReviews(filters, req.user)
      res.json(reviews)
    } catch (error) {
      logger.error('Error fetching performance reviews:', error)
      res.status(500).json({ error: 'Failed to fetch performance reviews' })
    }
  }
)

/**
 * @route PUT /api/performance/reviews/:reviewId
 * @desc Update performance review
 * @access HR, Manager, Assigned Reviewer
 */
router.put('/reviews/:reviewId',
  [
    param('reviewId').isUUID().withMessage('Invalid review ID'),
    body('status').optional().isIn(['draft', 'in_progress', 'completed', 'overdue']),
    body('selfAssessment').optional().isObject(),
    body('managerAssessment').optional().isObject(),
    body('goalRatings').optional().isArray(),
    body('competencyRatings').optional().isArray(),
    body('overallRating').optional().isNumeric().isFloat({ min: 1, max: 5 }),
    body('strengths').optional().isString(),
    body('areasForImprovement').optional().isString(),
    body('developmentPlan').optional().isString(),
    body('comments').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { reviewId } = req.params
      const review = await performanceService.updateReview(reviewId, req.body, req.user)
      
      if (!review) {
        return res.status(404).json({ error: 'Review not found or access denied' })
      }

      logger.info(`Performance review updated: ${reviewId}`, { userId: req.user.id })
      res.json(review)
    } catch (error) {
      logger.error('Error updating performance review:', error)
      res.status(500).json({ error: 'Failed to update performance review' })
    }
  }
)

/**
 * @route POST /api/performance/feedback
 * @desc Submit performance feedback
 * @access HR, Manager, Employee
 */
router.post('/feedback',
  [
    body('employeeId').isUUID().withMessage('Employee ID is required'),
    body('feedbackType').isIn(['peer', 'upward', 'downward', 'self', '360']),
    body('category').isIn(['performance', 'behavior', 'skills', 'goals', 'general']),
    body('rating').optional().isNumeric().isFloat({ min: 1, max: 5 }),
    body('comments').notEmpty().withMessage('Comments are required'),
    body('suggestions').optional().isString(),
    body('anonymous').isBoolean(),
    body('reviewId').optional().isUUID()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const feedback = await performanceService.submitFeedback(req.body, req.user.id)
      
      logger.info(`Performance feedback submitted: ${feedback.id}`, { 
        userId: req.user.id,
        employeeId: req.body.employeeId 
      })
      
      res.status(201).json(feedback)
    } catch (error) {
      logger.error('Error submitting performance feedback:', error)
      res.status(500).json({ error: 'Failed to submit performance feedback' })
    }
  }
)

/**
 * @route GET /api/performance/feedback/:employeeId
 * @desc Get performance feedback for employee
 * @access HR, Manager, Employee (own feedback)
 */
router.get('/feedback/:employeeId',
  [
    param('employeeId').isUUID().withMessage('Invalid employee ID'),
    query('feedbackType').optional().isIn(['peer', 'upward', 'downward', 'self', '360']),
    query('category').optional().isIn(['performance', 'behavior', 'skills', 'goals', 'general']),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { employeeId } = req.params
      
      // Check permissions
      if (!req.user.permissions.includes('hr') && 
          !req.user.permissions.includes('manager') && 
          req.user.employeeId !== employeeId) {
        return res.status(403).json({ error: 'Access denied' })
      }

      const feedback = await performanceService.getFeedback(employeeId, req.query, req.user)
      res.json(feedback)
    } catch (error) {
      logger.error('Error fetching performance feedback:', error)
      res.status(500).json({ error: 'Failed to fetch performance feedback' })
    }
  }
)

/**
 * @route GET /api/performance/analytics/overview
 * @desc Get performance analytics overview
 * @access HR Admin, Manager
 */
router.get('/analytics/overview',
  requirePermission('hr_admin', 'manager'),
  [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('department').optional().isUUID(),
    query('employeeId').optional().isUUID()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const analytics = await performanceService.getPerformanceAnalytics(req.query, req.user)
      res.json(analytics)
    } catch (error) {
      logger.error('Error fetching performance analytics:', error)
      res.status(500).json({ error: 'Failed to fetch performance analytics' })
    }
  }
)

/**
 * @route POST /api/performance/calibration
 * @desc Create performance calibration session
 * @access HR Admin
 */
router.post('/calibration',
  requirePermission('hr_admin'),
  [
    body('name').notEmpty().withMessage('Calibration session name is required'),
    body('description').optional().isString(),
    body('participants').isArray().withMessage('Participants must be an array'),
    body('participants.*').isUUID().withMessage('Invalid participant ID'),
    body('reviewIds').isArray().withMessage('Review IDs must be an array'),
    body('reviewIds.*').isUUID().withMessage('Invalid review ID'),
    body('scheduledDate').isISO8601().withMessage('Valid scheduled date is required'),
    body('guidelines').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const calibration = await performanceService.createCalibrationSession(req.body, req.user.id)
      
      logger.info(`Performance calibration session created: ${calibration.id}`, { 
        userId: req.user.id 
      })
      
      res.status(201).json(calibration)
    } catch (error) {
      logger.error('Error creating performance calibration session:', error)
      res.status(500).json({ error: 'Failed to create performance calibration session' })
    }
  }
)

export default router
