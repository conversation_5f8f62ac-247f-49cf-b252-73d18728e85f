"use client"

import { motion } from "framer-motion"
import {
  Users,
  DollarSign,
  TrendingUp,
  Calendar,
  Clock,
  Award,
  AlertTriangle,
  CheckCircle,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  Plus,
  Brain,
  Target,
  Zap,
  BarChart3,
  PieChart,
  Activity,
  Search,
  Filter,
  Download,
  RefreshCw
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { Header } from "@/components/layout/header"
import { formatCurrency, formatPercentage } from "@/lib/utils"
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer
} from "recharts"

// Mock data for dashboard
const stats = [
  {
    title: "Total Employees",
    value: "1,247",
    change: "+12%",
    changeType: "positive" as const,
    icon: Users,
    description: "vs last month"
  },
  {
    title: "Monthly Payroll",
    value: formatCurrency(2847500),
    change: "****%",
    changeType: "positive" as const,
    icon: DollarSign,
    description: "vs last month"
  },
  {
    title: "Avg Performance",
    value: "4.2/5.0",
    change: "+0.3",
    changeType: "positive" as const,
    icon: TrendingUp,
    description: "vs last quarter"
  },
  {
    title: "Attendance Rate",
    value: formatPercentage(94.8),
    change: "-1.2%",
    changeType: "negative" as const,
    icon: Clock,
    description: "vs last month"
  }
]

const recentActivities = [
  {
    id: 1,
    type: "hire",
    title: "New Employee Onboarded",
    description: "Sarah Johnson joined as Senior Developer",
    time: "2 hours ago",
    avatar: "/avatars/sarah.jpg",
    initials: "SJ"
  },
  {
    id: 2,
    type: "leave",
    title: "Leave Request Approved",
    description: "Mike Chen's vacation request for next week",
    time: "4 hours ago",
    avatar: "/avatars/mike.jpg",
    initials: "MC"
  },
  {
    id: 3,
    type: "performance",
    title: "Performance Review Completed",
    description: "Q4 review for Emily Davis - Exceeds Expectations",
    time: "1 day ago",
    avatar: "/avatars/emily.jpg",
    initials: "ED"
  },
  {
    id: 4,
    type: "payroll",
    title: "Payroll Processed",
    description: "December payroll completed for all employees",
    time: "2 days ago",
    avatar: null,
    initials: "PR"
  }
]

const upcomingEvents = [
  {
    id: 1,
    title: "All Hands Meeting",
    date: "Today, 2:00 PM",
    type: "meeting",
    attendees: 45
  },
  {
    id: 2,
    title: "Performance Reviews Due",
    date: "Tomorrow",
    type: "deadline",
    attendees: 12
  },
  {
    id: 3,
    title: "Team Building Event",
    date: "Friday, 10:00 AM",
    type: "event",
    attendees: 28
  }
]

const quickActions = [
  { name: "Add Employee", icon: Plus },
  { name: "Process Payroll", icon: DollarSign },
  { name: "Schedule Review", icon: Calendar },
  { name: "Generate Report", icon: TrendingUp }
]

// Chart data for analytics
const employeeGrowthData = [
  { month: "Jan", employees: 1150, hires: 45, departures: 12 },
  { month: "Feb", employees: 1180, hires: 38, departures: 8 },
  { month: "Mar", employees: 1195, hires: 25, departures: 10 },
  { month: "Apr", employees: 1210, hires: 32, departures: 17 },
  { month: "May", employees: 1225, hires: 28, departures: 13 },
  { month: "Jun", employees: 1247, hires: 35, departures: 13 }
]

const departmentData = [
  { name: "Engineering", value: 425, color: "#3B82F6" },
  { name: "Sales", value: 280, color: "#10B981" },
  { name: "Marketing", value: 165, color: "#F59E0B" },
  { name: "HR", value: 95, color: "#EF4444" },
  { name: "Finance", value: 85, color: "#8B5CF6" },
  { name: "Operations", value: 197, color: "#06B6D4" }
]

const performanceData = [
  { month: "Jan", avgRating: 4.1, satisfaction: 85 },
  { month: "Feb", avgRating: 4.0, satisfaction: 83 },
  { month: "Mar", avgRating: 4.2, satisfaction: 87 },
  { month: "Apr", avgRating: 4.1, satisfaction: 86 },
  { month: "May", avgRating: 4.3, satisfaction: 89 },
  { month: "Jun", avgRating: 4.2, satisfaction: 88 }
]

const aiInsights = [
  {
    id: 1,
    type: "prediction",
    title: "Attrition Risk Alert",
    description: "15 employees showing high attrition risk based on engagement patterns",
    confidence: 87,
    action: "Schedule 1:1 meetings",
    priority: "high"
  },
  {
    id: 2,
    type: "optimization",
    title: "Recruitment Efficiency",
    description: "Engineering team hiring could be 23% faster with process optimization",
    confidence: 92,
    action: "Review hiring pipeline",
    priority: "medium"
  },
  {
    id: 3,
    type: "insight",
    title: "Performance Correlation",
    description: "Remote workers show 12% higher satisfaction in Q2 performance reviews",
    confidence: 78,
    action: "Expand remote options",
    priority: "low"
  }
]

export default function DashboardPage() {
  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Dashboard"
        subtitle="Welcome back! Here's what's happening at your organization."
        actions={
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2 mr-4">
              <Input
                placeholder="Ask AI about your data..."
                className="w-64"
                leftIcon={<Search className="w-4 h-4" />}
              />
              <Button variant="outline" size="sm">
                <Brain className="w-4 h-4 mr-2" />
                AI Query
              </Button>
            </div>
            <Button variant="outline" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            {quickActions.slice(0, 2).map((action) => (
              <Button key={action.name} variant="outline" size="sm">
                <action.icon className="w-4 h-4 mr-2" />
                {action.name}
              </Button>
            ))}
          </div>
        }
      />

      {/* Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                  <span className={`flex items-center ${
                    stat.changeType === 'positive' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {stat.changeType === 'positive' ? (
                      <ArrowUpRight className="w-3 h-3 mr-1" />
                    ) : (
                      <ArrowDownRight className="w-3 h-3 mr-1" />
                    )}
                    {stat.change}
                  </span>
                  <span>{stat.description}</span>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* AI Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Brain className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                <CardTitle>AI Insights</CardTitle>
              </div>
              <Badge variant="outline" className="text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-800">
                <Zap className="w-3 h-3 mr-1" />
                Real-time
              </Badge>
            </div>
            <CardDescription>
              AI-powered insights and recommendations for your organization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {aiInsights.map((insight) => (
                <div
                  key={insight.id}
                  className={`p-4 rounded-lg border-l-4 ${
                    insight.priority === 'high' ? 'border-red-500 bg-red-500/10 dark:bg-red-500/20' :
                    insight.priority === 'medium' ? 'border-yellow-500 bg-yellow-500/10 dark:bg-yellow-500/20' :
                    'border-green-500 bg-green-500/10 dark:bg-green-500/20'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-foreground">{insight.title}</h4>
                    <Badge
                      variant={
                        insight.priority === 'high' ? 'destructive' :
                        insight.priority === 'medium' ? 'warning' : 'success'
                      }
                      className="text-xs"
                    >
                      {insight.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">{insight.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">
                      {insight.confidence}% confidence
                    </span>
                    <Button variant="outline" size="sm" className="text-xs">
                      {insight.action}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Employee Growth Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Employee Growth</CardTitle>
                  <CardDescription>
                    Hiring trends and workforce changes over time
                  </CardDescription>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={employeeGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="employees"
                    stackId="1"
                    stroke="#3B82F6"
                    fill="#3B82F6"
                    fillOpacity={0.6}
                    name="Total Employees"
                  />
                  <Area
                    type="monotone"
                    dataKey="hires"
                    stackId="2"
                    stroke="#10B981"
                    fill="#10B981"
                    fillOpacity={0.6}
                    name="New Hires"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </motion.div>

        {/* Department Distribution */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Department Distribution</CardTitle>
              <CardDescription>
                Employee distribution across departments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={departmentData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {departmentData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Performance Analytics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Performance Analytics</CardTitle>
                <CardDescription>
                  Employee performance and satisfaction trends
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Filter className="w-4 h-4 mr-2" />
                  Filter
                </Button>
                <Button variant="outline" size="sm">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  View Details
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis yAxisId="left" />
                <YAxis yAxisId="right" orientation="right" />
                <Tooltip />
                <Legend />
                <Line
                  yAxisId="left"
                  type="monotone"
                  dataKey="avgRating"
                  stroke="#3B82F6"
                  strokeWidth={3}
                  name="Avg Performance Rating"
                />
                <Line
                  yAxisId="right"
                  type="monotone"
                  dataKey="satisfaction"
                  stroke="#10B981"
                  strokeWidth={3}
                  name="Satisfaction %"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </motion.div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Recent Activities */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.8 }}
          className="lg:col-span-2"
        >
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Activities</CardTitle>
                  <CardDescription>
                    Latest updates from across your organization
                  </CardDescription>
                </div>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-4">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={activity.avatar} alt="" />
                      <AvatarFallback className="text-xs">
                        {activity.initials}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 space-y-1">
                      <p className="text-sm font-medium text-foreground">
                        {activity.title}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {activity.description}
                      </p>
                      <p className="text-xs text-muted-foreground">{activity.time}</p>
                    </div>
                    <Badge
                      variant={
                        activity.type === 'hire' ? 'success' :
                        activity.type === 'leave' ? 'warning' :
                        activity.type === 'performance' ? 'info' : 'secondary'
                      }
                      className="text-xs"
                    >
                      {activity.type}
                    </Badge>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <Button variant="outline" className="w-full">
                  View All Activities
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Upcoming Events */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.9 }}
        >
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Events</CardTitle>
              <CardDescription>
                Important dates and deadlines
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      event.type === 'meeting' ? 'bg-blue-500' :
                      event.type === 'deadline' ? 'bg-red-500' : 'bg-green-500'
                    }`} />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-foreground">
                        {event.title}
                      </p>
                      <p className="text-xs text-muted-foreground">{event.date}</p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {event.attendees}
                    </Badge>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <Button variant="outline" className="w-full">
                  <Calendar className="w-4 h-4 mr-2" />
                  View Calendar
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Quick Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.0 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>Quick Overview</CardTitle>
            <CardDescription>
              Key metrics at a glance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-green-500/10 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-foreground">Active Employees</p>
                  <p className="text-2xl font-bold text-foreground">1,189</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-yellow-500/10 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-foreground">Pending Reviews</p>
                  <p className="text-2xl font-bold text-foreground">23</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-500/10 rounded-lg">
                  <Award className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-foreground">Top Performers</p>
                  <p className="text-2xl font-bold text-foreground">156</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-purple-500/10 rounded-lg">
                  <Calendar className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-foreground">On Leave Today</p>
                  <p className="text-2xl font-bold text-foreground">12</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
