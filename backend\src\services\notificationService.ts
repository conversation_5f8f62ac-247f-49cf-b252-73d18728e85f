import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'

export class NotificationService {
  private db: DatabaseService

  constructor() {
    this.db = new DatabaseService()
  }

  async sendNotification(data: any): Promise<any> {
    try {
      const query = `
        INSERT INTO notifications (user_id, title, message, type, priority)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *
      `
      const result = await this.db.query(query, [
        data.userId,
        data.title,
        data.message,
        data.type || 'info',
        data.priority || 'normal'
      ])
      
      logger.info('Notification sent:', { 
        userId: data.userId, 
        title: data.title,
        type: data.type 
      })
      
      return result.rows[0]
    } catch (error) {
      logger.error('Error sending notification:', error)
      throw error
    }
  }

  async getNotifications(userId: string): Promise<any> {
    try {
      const query = `
        SELECT * FROM notifications 
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT 50
      `
      const result = await this.db.query(query, [userId])
      return result.rows
    } catch (error) {
      logger.error('Error fetching notifications:', error)
      throw error
    }
  }

  async markAsRead(notificationId: string, userId: string): Promise<any> {
    try {
      const query = `
        UPDATE notifications 
        SET read_at = NOW()
        WHERE id = $1 AND user_id = $2
        RETURNING *
      `
      const result = await this.db.query(query, [notificationId, userId])
      return result.rows[0]
    } catch (error) {
      logger.error('Error marking notification as read:', error)
      throw error
    }
  }

  async sendBulkNotification(userIds: string[], data: any): Promise<any> {
    try {
      const notifications = userIds.map(userId => ({
        userId,
        title: data.title,
        message: data.message,
        type: data.type || 'info',
        priority: data.priority || 'normal'
      }))

      // Send notifications to all users
      const promises = notifications.map(notification => 
        this.sendNotification(notification)
      )

      const results = await Promise.all(promises)
      return results
    } catch (error) {
      logger.error('Error sending bulk notifications:', error)
      throw error
    }
  }
}
