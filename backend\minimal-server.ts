import express from 'express'
import dotenv from 'dotenv'

// Load environment variables
dotenv.config()

console.log('🚀 Starting Minimal TypeScript Server...')

const app = express()
const PORT = process.env.PORT || 3001

console.log(`Server will run on port: ${PORT}`)

// Basic middleware
app.use(express.json())

// Health check route
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'Minimal TypeScript server is running'
  })
})

// Start server
try {
  const server = app.listen(PORT, () => {
    console.log(`🚀 Minimal TypeScript server running on port ${PORT}`)
    console.log(`🔗 Health check: http://localhost:${PORT}/health`)
  })

  server.on('error', (error) => {
    console.error('Server error:', error)
  })
} catch (error) {
  console.error('Failed to start server:', error)
  process.exit(1)
}

export default app
