import express from 'express'
import multer from 'multer'
import { body, param, query, validationResult } from 'express-validator'
import { DocumentService } from '../services/documentService'
import { requirePermission } from '../middleware/permissions'
import { logger } from '../utils/logger'

const router = express.Router()
const documentService = new DocumentService()

// Configure multer for file uploads
const storage = multer.memoryStorage()
const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ]
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('Invalid file type. Only PDF, Word, Excel, images, and text files are allowed.'))
    }
  }
})

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation Error',
      details: errors.array()
    })
  }
  next()
}

/**
 * @route POST /api/documents/upload
 * @desc Upload a document
 * @access HR, Manager, Employee (own documents)
 */
router.post('/upload',
  upload.single('document'),
  [
    body('employeeId').optional().isUUID(),
    body('category').isIn(['personal', 'contract', 'performance', 'training', 'compliance', 'other']),
    body('title').notEmpty().withMessage('Document title is required'),
    body('description').optional().isString(),
    body('confidential').isBoolean(),
    body('expiryDate').optional().isISO8601(),
    body('tags').optional().isArray()
  ],
  validateRequest,
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' })
      }

      // Check permissions for employee documents
      if (req.body.employeeId && 
          !req.user.permissions.includes('hr') && 
          !req.user.permissions.includes('manager') && 
          req.user.employeeId !== req.body.employeeId) {
        return res.status(403).json({ error: 'Access denied' })
      }

      const document = await documentService.uploadDocument(
        req.file,
        {
          employeeId: req.body.employeeId || req.user.employeeId,
          category: req.body.category,
          title: req.body.title,
          description: req.body.description,
          confidential: req.body.confidential,
          expiryDate: req.body.expiryDate,
          tags: req.body.tags
        },
        req.user.id
      )

      logger.info(`Document uploaded: ${document.id}`, { 
        userId: req.user.id,
        employeeId: req.body.employeeId 
      })

      res.status(201).json(document)
    } catch (error) {
      logger.error('Error uploading document:', error)
      res.status(500).json({ error: 'Failed to upload document' })
    }
  }
)

/**
 * @route GET /api/documents
 * @desc Get documents with filtering
 * @access HR, Manager, Employee (own documents)
 */
router.get('/',
  [
    query('employeeId').optional().isUUID(),
    query('category').optional().isIn(['personal', 'contract', 'performance', 'training', 'compliance', 'other']),
    query('confidential').optional().isBoolean(),
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('search').optional().isString(),
    query('tags').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      // Apply permission filters
      let filters = { ...req.query }
      
      if (!req.user.permissions.includes('hr') && !req.user.permissions.includes('manager')) {
        filters.employeeId = req.user.employeeId
      }

      const documents = await documentService.getDocuments(filters, req.user)
      res.json(documents)
    } catch (error) {
      logger.error('Error fetching documents:', error)
      res.status(500).json({ error: 'Failed to fetch documents' })
    }
  }
)

/**
 * @route GET /api/documents/:documentId
 * @desc Get document details
 * @access HR, Manager, Employee (own documents)
 */
router.get('/:documentId',
  [param('documentId').isUUID().withMessage('Invalid document ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { documentId } = req.params
      const document = await documentService.getDocumentById(documentId, req.user)
      
      if (!document) {
        return res.status(404).json({ error: 'Document not found' })
      }

      res.json(document)
    } catch (error) {
      logger.error('Error fetching document:', error)
      res.status(500).json({ error: 'Failed to fetch document' })
    }
  }
)

/**
 * @route GET /api/documents/:documentId/download
 * @desc Download document file
 * @access HR, Manager, Employee (own documents)
 */
router.get('/:documentId/download',
  [param('documentId').isUUID().withMessage('Invalid document ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { documentId } = req.params
      const { fileStream, document } = await documentService.downloadDocument(documentId, req.user)
      
      if (!fileStream || !document) {
        return res.status(404).json({ error: 'Document not found' })
      }

      // Set appropriate headers
      res.setHeader('Content-Type', document.mimeType)
      res.setHeader('Content-Disposition', `attachment; filename="${document.fileName}"`)
      res.setHeader('Content-Length', document.fileSize)

      // Log download
      await documentService.logDocumentAccess(documentId, 'download', req.user.id)

      fileStream.pipe(res)
    } catch (error) {
      logger.error('Error downloading document:', error)
      res.status(500).json({ error: 'Failed to download document' })
    }
  }
)

/**
 * @route PUT /api/documents/:documentId
 * @desc Update document metadata
 * @access HR, Manager, Document Owner
 */
router.put('/:documentId',
  [
    param('documentId').isUUID().withMessage('Invalid document ID'),
    body('title').optional().notEmpty(),
    body('description').optional().isString(),
    body('category').optional().isIn(['personal', 'contract', 'performance', 'training', 'compliance', 'other']),
    body('confidential').optional().isBoolean(),
    body('expiryDate').optional().isISO8601(),
    body('tags').optional().isArray()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { documentId } = req.params
      const document = await documentService.updateDocument(documentId, req.body, req.user)
      
      if (!document) {
        return res.status(404).json({ error: 'Document not found or access denied' })
      }

      logger.info(`Document updated: ${documentId}`, { userId: req.user.id })
      res.json(document)
    } catch (error) {
      logger.error('Error updating document:', error)
      res.status(500).json({ error: 'Failed to update document' })
    }
  }
)

/**
 * @route DELETE /api/documents/:documentId
 * @desc Delete document
 * @access HR Admin, Document Owner
 */
router.delete('/:documentId',
  [param('documentId').isUUID().withMessage('Invalid document ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { documentId } = req.params
      const success = await documentService.deleteDocument(documentId, req.user)
      
      if (!success) {
        return res.status(404).json({ error: 'Document not found or access denied' })
      }

      logger.info(`Document deleted: ${documentId}`, { userId: req.user.id })
      res.json({ message: 'Document deleted successfully' })
    } catch (error) {
      logger.error('Error deleting document:', error)
      res.status(500).json({ error: 'Failed to delete document' })
    }
  }
)

/**
 * @route POST /api/documents/:documentId/share
 * @desc Share document with other users
 * @access HR, Manager, Document Owner
 */
router.post('/:documentId/share',
  [
    param('documentId').isUUID().withMessage('Invalid document ID'),
    body('userIds').isArray().withMessage('User IDs must be an array'),
    body('userIds.*').isUUID().withMessage('Invalid user ID'),
    body('permissions').isIn(['view', 'download', 'edit']),
    body('expiryDate').optional().isISO8601(),
    body('message').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { documentId } = req.params
      const sharing = await documentService.shareDocument(
        documentId,
        req.body,
        req.user
      )
      
      if (!sharing) {
        return res.status(404).json({ error: 'Document not found or access denied' })
      }

      logger.info(`Document shared: ${documentId}`, { 
        userId: req.user.id,
        sharedWith: req.body.userIds 
      })
      
      res.json(sharing)
    } catch (error) {
      logger.error('Error sharing document:', error)
      res.status(500).json({ error: 'Failed to share document' })
    }
  }
)

/**
 * @route GET /api/documents/:documentId/versions
 * @desc Get document version history
 * @access HR, Manager, Document Owner
 */
router.get('/:documentId/versions',
  [param('documentId').isUUID().withMessage('Invalid document ID')],
  validateRequest,
  async (req, res) => {
    try {
      const { documentId } = req.params
      const versions = await documentService.getDocumentVersions(documentId, req.user)
      
      if (!versions) {
        return res.status(404).json({ error: 'Document not found or access denied' })
      }

      res.json(versions)
    } catch (error) {
      logger.error('Error fetching document versions:', error)
      res.status(500).json({ error: 'Failed to fetch document versions' })
    }
  }
)

/**
 * @route POST /api/documents/:documentId/new-version
 * @desc Upload new version of existing document
 * @access HR, Manager, Document Owner
 */
router.post('/:documentId/new-version',
  upload.single('document'),
  [
    param('documentId').isUUID().withMessage('Invalid document ID'),
    body('versionNotes').optional().isString()
  ],
  validateRequest,
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' })
      }

      const { documentId } = req.params
      const newVersion = await documentService.uploadNewVersion(
        documentId,
        req.file,
        req.body.versionNotes,
        req.user
      )
      
      if (!newVersion) {
        return res.status(404).json({ error: 'Document not found or access denied' })
      }

      logger.info(`New document version uploaded: ${documentId}`, { 
        userId: req.user.id,
        version: newVersion.version 
      })
      
      res.status(201).json(newVersion)
    } catch (error) {
      logger.error('Error uploading new document version:', error)
      res.status(500).json({ error: 'Failed to upload new document version' })
    }
  }
)

/**
 * @route GET /api/documents/analytics/usage
 * @desc Get document usage analytics
 * @access HR Admin
 */
router.get('/analytics/usage',
  requirePermission('hr_admin'),
  [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('category').optional().isIn(['personal', 'contract', 'performance', 'training', 'compliance', 'other'])
  ],
  validateRequest,
  async (req, res) => {
    try {
      const analytics = await documentService.getDocumentAnalytics(req.query)
      res.json(analytics)
    } catch (error) {
      logger.error('Error fetching document analytics:', error)
      res.status(500).json({ error: 'Failed to fetch document analytics' })
    }
  }
)

export default router
