{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/PeopleNest/peoplenest-ui/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useEffect } from \"react\"\nimport { useRouter } from \"next/navigation\"\nimport { motion } from \"framer-motion\"\nimport { Building2, Loader2 } from \"lucide-react\"\n\nexport default function Home() {\n  const router = useRouter()\n\n  useEffect(() => {\n    // Simulate loading and redirect to dashboard\n    const timer = setTimeout(() => {\n      router.push(\"/dashboard\")\n    }, 2000)\n\n    return () => clearTimeout(timer)\n  }, [router])\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary/5 via-background to-primary/10 flex items-center justify-center\">\n      <motion.div\n        initial={{ opacity: 0, scale: 0.8 }}\n        animate={{ opacity: 1, scale: 1 }}\n        transition={{ duration: 0.5 }}\n        className=\"text-center\"\n      >\n        <motion.div\n          initial={{ scale: 0.8 }}\n          animate={{ scale: 1 }}\n          transition={{ delay: 0.2, duration: 0.3 }}\n          className=\"inline-flex items-center justify-center w-20 h-20 bg-primary rounded-3xl mb-6 shadow-lg\"\n        >\n          <Building2 className=\"w-10 h-10 text-primary-foreground\" />\n        </motion.div>\n\n        <motion.h1\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.4, duration: 0.3 }}\n          className=\"text-4xl font-bold text-foreground mb-4\"\n        >\n          PeopleNest\n        </motion.h1>\n\n        <motion.p\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.6, duration: 0.3 }}\n          className=\"text-xl text-muted-foreground mb-8\"\n        >\n          Enterprise HRMS Platform\n        </motion.p>\n\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.8, duration: 0.3 }}\n          className=\"flex items-center justify-center space-x-2 text-muted-foreground\"\n        >\n          <Loader2 className=\"w-5 h-5 animate-spin\" />\n          <span>Loading your workspace...</span>\n        </motion.div>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6CAA6C;QAC7C,MAAM,QAAQ,WAAW;YACvB,OAAO,IAAI,CAAC;QACd,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;oBAAI;oBACtB,SAAS;wBAAE,OAAO;oBAAE;oBACpB,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;8BAGvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BACX;;;;;;8BAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BACX;;;;;;8BAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}]}