import { DatabaseService } from '../services/databaseService'
import { logger } from './logger'
import { Request, Response, NextFunction } from 'express'

const db = new DatabaseService()

export type AuditAction = 'CREATE' | 'READ' | 'UPDATE' | 'DELETE' | 'LOGIN' | 'LOGOUT' | 'EXPORT' | 'IMPORT'

export interface AuditLogEntry {
  id?: string
  table_name: string
  record_id: string
  action: AuditAction
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  user_id: string
  ip_address?: string
  user_agent?: string
  timestamp?: Date
  session_id?: string
  request_id?: string
  data_classification?: 'PUBLIC' | 'INTERNAL' | 'CONFIDENTIAL' | 'RESTRICTED'
  legal_basis?: string
  purpose?: string
}

export interface DataAccessLog {
  id?: string
  user_id: string
  employee_id: string
  data_type: string
  action: string
  purpose: string
  legal_basis: string
  ip_address?: string
  user_agent?: string
  timestamp?: Date
  retention_date?: Date
}

class AuditLogger {
  /**
   * Log a general audit event
   */
  async logAuditEvent(entry: AuditLogEntry): Promise<void> {
    try {
      const query = `
        INSERT INTO audit_logs (
          table_name, record_id, action, old_values, new_values,
          user_id, ip_address, user_agent, session_id, request_id,
          data_classification, legal_basis, purpose, timestamp
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      `

      const values = [
        entry.table_name,
        entry.record_id,
        entry.action,
        entry.old_values ? JSON.stringify(entry.old_values) : null,
        entry.new_values ? JSON.stringify(entry.new_values) : null,
        entry.user_id,
        entry.ip_address,
        entry.user_agent,
        entry.session_id,
        entry.request_id,
        entry.data_classification || 'INTERNAL',
        entry.legal_basis,
        entry.purpose,
        entry.timestamp || new Date()
      ]

      await db.query(query, values)

      // Also log to application logger for immediate monitoring
      logger.info('Audit event logged', {
        event: 'audit_log',
        table: entry.table_name,
        action: entry.action,
        userId: entry.user_id,
        recordId: entry.record_id,
        timestamp: entry.timestamp || new Date()
      })
    } catch (error) {
      logger.error('Failed to log audit event:', error)
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Log data access for GDPR compliance
   */
  async logDataAccess(entry: DataAccessLog): Promise<void> {
    try {
      const retentionDate = this.calculateRetentionDate(entry.data_type)
      
      const query = `
        INSERT INTO data_access_logs (
          user_id, employee_id, data_type, action, purpose, legal_basis,
          ip_address, user_agent, timestamp, retention_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      `

      const values = [
        entry.user_id,
        entry.employee_id,
        entry.data_type,
        entry.action,
        entry.purpose,
        entry.legal_basis,
        entry.ip_address,
        entry.user_agent,
        entry.timestamp || new Date(),
        retentionDate
      ]

      await db.query(query, values)

      logger.info('Data access logged', {
        event: 'data_access',
        userId: entry.user_id,
        employeeId: entry.employee_id,
        dataType: entry.data_type,
        action: entry.action,
        purpose: entry.purpose,
        legalBasis: entry.legal_basis
      })
    } catch (error) {
      logger.error('Failed to log data access:', error)
    }
  }

  /**
   * Log employee data operations
   */
  async logEmployeeOperation(
    action: AuditAction,
    employeeId: string,
    userId: string,
    oldData?: any,
    newData?: any,
    req?: Request
  ): Promise<void> {
    const entry: AuditLogEntry = {
      table_name: 'employees',
      record_id: employeeId,
      action,
      old_values: oldData,
      new_values: newData,
      user_id: userId,
      ip_address: req?.ip,
      user_agent: req?.get('User-Agent'),
      session_id: req?.sessionID,
      request_id: req?.headers['x-request-id'] as string,
      data_classification: 'CONFIDENTIAL',
      legal_basis: 'Legitimate Interest - HR Management',
      purpose: 'Employee Record Management'
    }

    await this.logAuditEvent(entry)

    // Also log data access for GDPR
    if (action === 'READ') {
      await this.logDataAccess({
        user_id: userId,
        employee_id: employeeId,
        data_type: 'employee_profile',
        action: 'view',
        purpose: 'HR Management',
        legal_basis: 'Legitimate Interest',
        ip_address: req?.ip,
        user_agent: req?.get('User-Agent')
      })
    }
  }

  /**
   * Log authentication events
   */
  async logAuthEvent(
    action: 'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED' | 'PASSWORD_RESET',
    userId: string,
    email: string,
    req?: Request,
    additionalData?: Record<string, any>
  ): Promise<void> {
    const entry: AuditLogEntry = {
      table_name: 'users',
      record_id: userId,
      action: action as AuditAction,
      new_values: {
        email,
        ...additionalData
      },
      user_id: userId,
      ip_address: req?.ip,
      user_agent: req?.get('User-Agent'),
      session_id: req?.sessionID,
      data_classification: 'INTERNAL',
      purpose: 'Authentication and Access Control'
    }

    await this.logAuditEvent(entry)
  }

  /**
   * Log payroll operations
   */
  async logPayrollOperation(
    action: AuditAction,
    payrollId: string,
    employeeId: string,
    userId: string,
    oldData?: any,
    newData?: any,
    req?: Request
  ): Promise<void> {
    const entry: AuditLogEntry = {
      table_name: 'payroll_records',
      record_id: payrollId,
      action,
      old_values: oldData,
      new_values: newData,
      user_id: userId,
      ip_address: req?.ip,
      user_agent: req?.get('User-Agent'),
      data_classification: 'RESTRICTED',
      legal_basis: 'Legal Obligation - Payroll Processing',
      purpose: 'Payroll Management and Tax Compliance'
    }

    await this.logAuditEvent(entry)

    // Log data access for the employee whose payroll was accessed
    await this.logDataAccess({
      user_id: userId,
      employee_id: employeeId,
      data_type: 'payroll_data',
      action: action.toLowerCase(),
      purpose: 'Payroll Processing',
      legal_basis: 'Legal Obligation',
      ip_address: req?.ip,
      user_agent: req?.get('User-Agent')
    })
  }

  /**
   * Log data export operations
   */
  async logDataExport(
    userId: string,
    exportType: string,
    employeeIds: string[],
    req?: Request
  ): Promise<void> {
    const entry: AuditLogEntry = {
      table_name: 'data_exports',
      record_id: `export_${Date.now()}`,
      action: 'EXPORT',
      new_values: {
        export_type: exportType,
        employee_count: employeeIds.length,
        employee_ids: employeeIds
      },
      user_id: userId,
      ip_address: req?.ip,
      user_agent: req?.get('User-Agent'),
      data_classification: 'CONFIDENTIAL',
      legal_basis: 'Legitimate Interest - Business Operations',
      purpose: 'Data Export for Business Analysis'
    }

    await this.logAuditEvent(entry)

    // Log data access for each employee
    for (const employeeId of employeeIds) {
      await this.logDataAccess({
        user_id: userId,
        employee_id: employeeId,
        data_type: 'employee_export',
        action: 'export',
        purpose: 'Business Analysis',
        legal_basis: 'Legitimate Interest',
        ip_address: req?.ip,
        user_agent: req?.get('User-Agent')
      })
    }
  }

  /**
   * Get audit trail for a specific record
   */
  async getAuditTrail(tableName: string, recordId: string, limit: number = 100): Promise<AuditLogEntry[]> {
    try {
      const query = `
        SELECT 
          id, table_name, record_id, action, old_values, new_values,
          user_id, ip_address, user_agent, timestamp, data_classification,
          legal_basis, purpose
        FROM audit_logs 
        WHERE table_name = $1 AND record_id = $2 
        ORDER BY timestamp DESC 
        LIMIT $3
      `

      const result = await db.query(query, [tableName, recordId, limit])
      return result.rows.map(row => ({
        ...row,
        old_values: row.old_values ? JSON.parse(row.old_values) : null,
        new_values: row.new_values ? JSON.parse(row.new_values) : null
      }))
    } catch (error) {
      logger.error('Failed to get audit trail:', error)
      return []
    }
  }

  /**
   * Get data access logs for GDPR compliance
   */
  async getDataAccessLogs(employeeId: string, limit: number = 100): Promise<DataAccessLog[]> {
    try {
      const query = `
        SELECT * FROM data_access_logs 
        WHERE employee_id = $1 
        ORDER BY timestamp DESC 
        LIMIT $2
      `

      const result = await db.query(query, [employeeId, limit])
      return result.rows
    } catch (error) {
      logger.error('Failed to get data access logs:', error)
      return []
    }
  }

  /**
   * Calculate retention date based on data type
   */
  private calculateRetentionDate(dataType: string): Date {
    const retentionPeriods: Record<string, number> = {
      'employee_profile': 7 * 365, // 7 years
      'payroll_data': 7 * 365, // 7 years
      'performance_review': 3 * 365, // 3 years
      'audit_logs': 7 * 365, // 7 years
      'training_records': 5 * 365, // 5 years
      'default': 7 * 365 // 7 years default
    }

    const days = retentionPeriods[dataType] || retentionPeriods.default
    const retentionDate = new Date()
    retentionDate.setDate(retentionDate.getDate() + days)
    
    return retentionDate
  }

  /**
   * Clean up expired audit logs
   */
  async cleanupExpiredLogs(): Promise<void> {
    try {
      const query = `
        DELETE FROM data_access_logs 
        WHERE retention_date < NOW()
      `

      const result = await db.query(query)
      logger.info(`Cleaned up ${result.rowCount} expired audit logs`)
    } catch (error) {
      logger.error('Failed to cleanup expired logs:', error)
    }
  }
}

// Singleton instance
export const auditLogger = new AuditLogger()

/**
 * Express middleware to automatically log API requests
 */
export const auditMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Store original end function
  const originalEnd = res.end

  // Override end function to log after response
  res.end = function(chunk?: any, encoding?: any, cb?: () => void) {
    // Log the request if it modifies data
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method) && req.user) {
      const action = getActionFromMethod(req.method)
      const tableName = getTableFromPath(req.path)
      const recordId = req.params.id || req.body.id || 'unknown'

      auditLogger.logAuditEvent({
        table_name: tableName,
        record_id: recordId,
        action,
        new_values: req.body,
        user_id: req.user.id,
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        session_id: req.sessionID,
        request_id: req.headers['x-request-id'] as string
      }).catch(error => {
        logger.error('Audit middleware failed:', error)
      })
    }

    // Call original end function
    return originalEnd.call(this, chunk, encoding, cb)
  }

  next()
}

function getActionFromMethod(method: string): AuditAction {
  switch (method) {
    case 'POST': return 'CREATE'
    case 'PUT':
    case 'PATCH': return 'UPDATE'
    case 'DELETE': return 'DELETE'
    default: return 'READ' as AuditAction
  }
}

function getTableFromPath(path: string): string {
  // Extract table name from API path
  const segments = path.split('/')
  const apiIndex = segments.indexOf('api')
  return apiIndex >= 0 && segments[apiIndex + 1] ? segments[apiIndex + 1] : 'unknown'
}

export default auditLogger
