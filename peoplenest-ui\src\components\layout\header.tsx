"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
  Search,
  Bell,
  MessageSquare,
  Settings,
  User,
  LogOut,
  Moon,
  Sun,
  Globe,
  ChevronDown,
  Plus,
  Filter,
  Download,
  RefreshCw
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface HeaderProps {
  title?: string
  subtitle?: string
  actions?: React.ReactNode
  className?: string
}

export function Header({ title, subtitle, actions, className }: HeaderProps) {
  const [showNotifications, setShowNotifications] = useState(false)
  const [showProfile, setShowProfile] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  const notifications = [
    {
      id: 1,
      title: "New Employee Onboarding",
      message: "<PERSON> has completed her onboarding checklist",
      time: "2 minutes ago",
      unread: true,
      type: "success"
    },
    {
      id: 2,
      title: "Leave Request Pending",
      message: "Mike <PERSON> has requested 3 days of annual leave",
      time: "1 hour ago",
      unread: true,
      type: "warning"
    },
    {
      id: 3,
      title: "Performance Review Due",
      message: "5 performance reviews are due this week",
      time: "3 hours ago",
      unread: false,
      type: "info"
    }
  ]

  const quickActions = [
    { name: "Add Employee", icon: Plus, action: () => {} },
    { name: "Export Data", icon: Download, action: () => {} },
    { name: "Refresh", icon: RefreshCw, action: () => {} },
    { name: "Filter", icon: Filter, action: () => {} },
  ]

  return (
    <header className={cn(
      "sticky top-0 z-40 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm",
      className
    )}>
      <div className="flex h-16 items-center justify-between px-6">
        {/* Left Section - Title and Breadcrumb */}
        <div className="flex items-center space-x-4">
          <div>
            {title && (
              <h1 className="text-xl font-semibold text-foreground">{title}</h1>
            )}
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 max-w-md mx-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search employees, documents, or anything..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 w-full bg-muted border-border focus:bg-background transition-colors"
            />
          </div>
        </div>

        {/* Right Section - Actions and Profile */}
        <div className="flex items-center space-x-3">
          {/* Quick Actions */}
          {actions && (
            <div className="flex items-center space-x-2 mr-4">
              {actions}
            </div>
          )}

          {/* Default Quick Actions */}
          <div className="hidden md:flex items-center space-x-1">
            {quickActions.map((action) => (
              <Button
                key={action.name}
                variant="ghost"
                size="icon"
                onClick={action.action}
                className="h-9 w-9 text-muted-foreground hover:text-foreground"
                title={action.name}
              >
                <action.icon className="h-4 w-4" />
              </Button>
            ))}
          </div>

          {/* Notifications */}
          <div className="relative">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowNotifications(!showNotifications)}
              className="h-9 w-9 text-muted-foreground hover:text-foreground relative"
            >
              <Bell className="h-4 w-4" />
              {notifications.some(n => n.unread) && (
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full" />
              )}
            </Button>

            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="absolute right-0 mt-2 w-80 z-50"
                >
                  <Card className="shadow-lg border-border">
                    <CardContent className="p-0">
                      <div className="p-4 border-b border-border">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-foreground">Notifications</h3>
                          <Badge variant="secondary" className="text-xs">
                            {notifications.filter(n => n.unread).length} new
                          </Badge>
                        </div>
                      </div>
                      <div className="max-h-80 overflow-y-auto">
                        {notifications.map((notification) => (
                          <div
                            key={notification.id}
                            className={cn(
                              "p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors",
                              notification.unread && "bg-primary/5"
                            )}
                          >
                            <div className="flex items-start space-x-3">
                              <div className={cn(
                                "w-2 h-2 rounded-full mt-2 flex-shrink-0",
                                notification.type === "success" && "bg-green-500",
                                notification.type === "warning" && "bg-yellow-500",
                                notification.type === "info" && "bg-primary"
                              )} />
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-foreground">
                                  {notification.title}
                                </p>
                                <p className="text-sm text-muted-foreground mt-1">
                                  {notification.message}
                                </p>
                                <p className="text-xs text-muted-foreground/70 mt-2">
                                  {notification.time}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="p-3 border-t border-border">
                        <Button variant="ghost" className="w-full text-sm">
                          View all notifications
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Messages */}
          <Button
            variant="ghost"
            size="icon"
            className="h-9 w-9 text-muted-foreground hover:text-foreground"
          >
            <MessageSquare className="h-4 w-4" />
          </Button>

          {/* Profile Dropdown */}
          <div className="relative">
            <Button
              variant="ghost"
              onClick={() => setShowProfile(!showProfile)}
              className="flex items-center space-x-2 h-9 px-3 text-foreground hover:text-foreground/80"
            >
              <Avatar className="h-7 w-7">
                <AvatarImage src="/avatars/user.jpg" alt="User" />
                <AvatarFallback>JD</AvatarFallback>
              </Avatar>
              <ChevronDown className="h-3 w-3" />
            </Button>

            <AnimatePresence>
              {showProfile && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="absolute right-0 mt-2 w-56 z-50"
                >
                  <Card className="shadow-lg border-gray-200">
                    <CardContent className="p-0">
                      <div className="p-4 border-b border-gray-200">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src="/avatars/user.jpg" alt="User" />
                            <AvatarFallback>JD</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-foreground">John Doe</p>
                            <p className="text-sm text-muted-foreground">HR Manager</p>
                          </div>
                        </div>
                      </div>
                      <div className="py-2">
                        {[
                          { icon: User, label: "Profile", href: "/dashboard" },
                          { icon: Settings, label: "Settings", href: "/dashboard" },
                          { icon: Moon, label: "Dark Mode", href: "#" },
                          { icon: Globe, label: "Language", href: "#" },
                        ].map((item) => (
                          <button
                            key={item.label}
                            className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-muted transition-colors"
                          >
                            <item.icon className="h-4 w-4" />
                            <span>{item.label}</span>
                          </button>
                        ))}
                        <hr className="my-2" />
                        <button className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors">
                          <LogOut className="h-4 w-4" />
                          <span>Sign out</span>
                        </button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </header>
  )
}
