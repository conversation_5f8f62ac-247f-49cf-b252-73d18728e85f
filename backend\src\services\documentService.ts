import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'

export class DocumentService {
  private db: DatabaseService

  constructor() {
    this.db = new DatabaseService()
  }

  async getDocuments(filters: any, user: any): Promise<any> {
    try {
      // Basic implementation - can be expanded later
      const query = `
        SELECT * FROM documents 
        WHERE 1=1
        ORDER BY created_at DESC
      `
      const result = await this.db.query(query)
      return result.rows
    } catch (error) {
      logger.error('Error fetching documents:', error)
      throw error
    }
  }

  async getDocument(id: string, user: any): Promise<any> {
    try {
      const query = `
        SELECT * FROM documents 
        WHERE id = $1
      `
      const result = await this.db.query(query, [id])
      return result.rows[0]
    } catch (error) {
      logger.error('Error fetching document:', error)
      throw error
    }
  }

  async createDocument(data: any, user: any): Promise<any> {
    try {
      const query = `
        INSERT INTO documents (name, type, content, created_by)
        VALUES ($1, $2, $3, $4)
        RETURNING *
      `
      const result = await this.db.query(query, [
        data.name,
        data.type,
        data.content,
        user.id
      ])
      return result.rows[0]
    } catch (error) {
      logger.error('Error creating document:', error)
      throw error
    }
  }

  async updateDocument(id: string, data: any, user: any): Promise<any> {
    try {
      const query = `
        UPDATE documents 
        SET name = $1, type = $2, content = $3, updated_at = NOW()
        WHERE id = $4
        RETURNING *
      `
      const result = await this.db.query(query, [
        data.name,
        data.type,
        data.content,
        id
      ])
      return result.rows[0]
    } catch (error) {
      logger.error('Error updating document:', error)
      throw error
    }
  }

  async deleteDocument(id: string, user: any): Promise<boolean> {
    try {
      const query = `DELETE FROM documents WHERE id = $1`
      await this.db.query(query, [id])
      return true
    } catch (error) {
      logger.error('Error deleting document:', error)
      throw error
    }
  }
}
