import express from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { authMiddleware } from '../middleware/auth'
import { requirePermission } from '../middleware/permissions'
import { auditLogger } from '../utils/auditLogger'
import { gdprService } from '../services/gdprService'
import { logger } from '../utils/logger'
import { DatabaseService } from '../services/databaseService'
import {
  classifyData,
  auditSensitiveOperation,
  sensitiveDataRateLimit
} from '../middleware/security'

const db = new DatabaseService()

const router = express.Router()

// Apply authentication to all routes
router.use(authMiddleware)

/**
 * GDPR Data Subject Requests
 */

// Create a new data subject request
router.post('/gdpr/request',
  sensitiveDataRateLimit,
  classifyData('RESTRICTED'),
  auditSensitiveOperation('GDPR_REQUEST_CREATE'),
  [
    body('employee_id').isUUID().withMessage('Valid employee ID is required'),
    body('request_type').isIn(['access', 'rectification', 'erasure', 'portability', 'restriction'])
      .withMessage('Valid request type is required'),
    body('requested_data').optional().isArray().withMessage('Requested data must be an array'),
    body('reason').optional().isString().trim().isLength({ max: 1000 })
      .withMessage('Reason must be a string with max 1000 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { employee_id, request_type, requested_data, reason } = req.body

      // Check if user has permission to make requests for this employee
      if (employee_id !== req.user.employee_id && !req.user.permissions.includes('hr')) {
        return res.status(403).json({
          error: 'Insufficient permissions to create request for this employee'
        })
      }

      const request = await gdprService.createDataSubjectRequest({
        employee_id,
        request_type,
        requested_data,
        requested_by: req.user.id,
        reason
      })

      res.status(201).json({
        message: 'Data subject request created successfully',
        request
      })
    } catch (error) {
      logger.error('Failed to create GDPR request:', error)
      res.status(500).json({
        error: 'Failed to create data subject request'
      })
    }
  }
)

// Get data subject requests
router.get('/gdpr/requests',
  requirePermission('hr'),
  classifyData('RESTRICTED'),
  [
    query('employee_id').optional().isUUID().withMessage('Valid employee ID required'),
    query('status').optional().isIn(['pending', 'in_progress', 'completed', 'rejected'])
      .withMessage('Valid status required'),
    query('request_type').optional().isIn(['access', 'rectification', 'erasure', 'portability', 'restriction'])
      .withMessage('Valid request type required'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { employee_id, status, request_type, limit = 50, offset = 0 } = req.query

      // Build query based on filters
      let query = 'SELECT * FROM data_subject_requests WHERE 1=1'
      const params: any[] = []
      let paramIndex = 1

      if (employee_id) {
        query += ` AND employee_id = $${paramIndex++}`
        params.push(employee_id)
      }

      if (status) {
        query += ` AND status = $${paramIndex++}`
        params.push(status)
      }

      if (request_type) {
        query += ` AND request_type = $${paramIndex++}`
        params.push(request_type)
      }

      query += ` ORDER BY requested_at DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`
      params.push(limit, offset)

      const result = await db.query(query, params)

      res.json({
        requests: result.rows,
        pagination: {
          limit: parseInt(limit as string),
          offset: parseInt(offset as string),
          total: result.rowCount
        }
      })
    } catch (error) {
      logger.error('Failed to get GDPR requests:', error)
      res.status(500).json({
        error: 'Failed to retrieve data subject requests'
      })
    }
  }
)

// Process a data subject request
router.post('/gdpr/requests/:id/process',
  requirePermission('hr_admin'),
  classifyData('RESTRICTED'),
  auditSensitiveOperation('GDPR_REQUEST_PROCESS'),
  [
    param('id').isUUID().withMessage('Valid request ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { id } = req.params

      // Get request details to determine type
      const requestQuery = 'SELECT * FROM data_subject_requests WHERE id = $1'
      const requestResult = await db.query(requestQuery, [id])

      if (requestResult.rows.length === 0) {
        return res.status(404).json({
          error: 'Data subject request not found'
        })
      }

      const request = requestResult.rows[0]

      let result
      switch (request.request_type) {
        case 'access':
          result = await gdprService.processAccessRequest(id, req.user.id)
          break
        case 'erasure':
          await gdprService.processErasureRequest(id, req.user.id)
          result = { message: 'Data erasure completed' }
          break
        case 'portability':
          result = await gdprService.processPortabilityRequest(id, req.user.id)
          break
        default:
          return res.status(400).json({
            error: `Processing for ${request.request_type} requests not yet implemented`
          })
      }

      res.json({
        message: 'Request processed successfully',
        result
      })
    } catch (error) {
      logger.error('Failed to process GDPR request:', error)
      res.status(500).json({
        error: error.message || 'Failed to process data subject request'
      })
    }
  }
)

/**
 * Consent Management
 */

// Record consent
router.post('/gdpr/consent',
  classifyData('RESTRICTED'),
  auditSensitiveOperation('CONSENT_RECORD'),
  [
    body('employee_id').isUUID().withMessage('Valid employee ID is required'),
    body('consent_type').isString().trim().isLength({ min: 1, max: 100 })
      .withMessage('Consent type is required'),
    body('consent_given').isBoolean().withMessage('Consent given must be boolean'),
    body('purpose').isString().trim().isLength({ min: 1, max: 500 })
      .withMessage('Purpose is required'),
    body('legal_basis').isString().trim().isLength({ min: 1, max: 100 })
      .withMessage('Legal basis is required'),
    body('data_categories').isArray().withMessage('Data categories must be an array'),
    body('retention_period').isInt({ min: 1 }).withMessage('Retention period must be positive integer'),
    body('third_parties').optional().isArray().withMessage('Third parties must be an array')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const consentData = req.body

      // Check if user can record consent for this employee
      if (consentData.employee_id !== req.user.employee_id && !req.user.permissions.includes('hr')) {
        return res.status(403).json({
          error: 'Insufficient permissions to record consent for this employee'
        })
      }

      const consent = await gdprService.recordConsent(consentData)

      res.status(201).json({
        message: 'Consent recorded successfully',
        consent
      })
    } catch (error) {
      logger.error('Failed to record consent:', error)
      res.status(500).json({
        error: 'Failed to record consent'
      })
    }
  }
)

// Withdraw consent
router.post('/gdpr/consent/:id/withdraw',
  classifyData('RESTRICTED'),
  auditSensitiveOperation('CONSENT_WITHDRAW'),
  [
    param('id').isUUID().withMessage('Valid consent ID is required')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { id } = req.params

      await gdprService.withdrawConsent(id, req.user.employee_id)

      res.json({
        message: 'Consent withdrawn successfully'
      })
    } catch (error) {
      logger.error('Failed to withdraw consent:', error)
      res.status(500).json({
        error: 'Failed to withdraw consent'
      })
    }
  }
)

/**
 * Audit and Security
 */

// Get audit trail for a record
router.get('/audit/:table/:recordId',
  requirePermission('hr_admin'),
  classifyData('RESTRICTED'),
  [
    param('table').isString().trim().isLength({ min: 1, max: 50 })
      .withMessage('Valid table name is required'),
    param('recordId').isString().trim().isLength({ min: 1, max: 255 })
      .withMessage('Valid record ID is required'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { table, recordId } = req.params
      const { limit = 50 } = req.query

      const auditTrail = await auditLogger.getAuditTrail(table, recordId, parseInt(limit as string))

      res.json({
        audit_trail: auditTrail,
        table,
        record_id: recordId
      })
    } catch (error) {
      logger.error('Failed to get audit trail:', error)
      res.status(500).json({
        error: 'Failed to retrieve audit trail'
      })
    }
  }
)

// Get data access logs for an employee
router.get('/audit/data-access/:employeeId',
  requirePermission('hr_admin'),
  classifyData('RESTRICTED'),
  [
    param('employeeId').isUUID().withMessage('Valid employee ID is required'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { employeeId } = req.params
      const { limit = 50 } = req.query

      const accessLogs = await auditLogger.getDataAccessLogs(employeeId, parseInt(limit as string))

      res.json({
        access_logs: accessLogs,
        employee_id: employeeId
      })
    } catch (error) {
      logger.error('Failed to get data access logs:', error)
      res.status(500).json({
        error: 'Failed to retrieve data access logs'
      })
    }
  }
)

// Security health check
router.get('/health',
  requirePermission('hr_admin'),
  classifyData('INTERNAL'),
  async (req, res) => {
    try {
      // Check various security components
      const healthChecks = {
        encryption: {
          status: 'healthy',
          last_check: new Date()
        },
        audit_logging: {
          status: 'healthy',
          last_check: new Date()
        },
        gdpr_compliance: {
          status: 'healthy',
          last_check: new Date()
        },
        data_retention: {
          status: 'healthy',
          last_check: new Date()
        }
      }

      res.json({
        status: 'healthy',
        components: healthChecks,
        timestamp: new Date()
      })
    } catch (error) {
      logger.error('Security health check failed:', error)
      res.status(500).json({
        status: 'unhealthy',
        error: 'Security health check failed'
      })
    }
  }
)

export default router
