"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import {
  DollarSign,
  Calendar,
  Download,
  Upload,
  FileText,
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Filter,
  Search,
  MoreHorizontal,
  Eye,
  Edit,
  Send,
  Calculator,
  CreditCard,
  Building,
  User,
  Mail,
  Phone
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Header } from "@/components/layout/header"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Mock payroll data
const payrollData = [
  {
    id: 1,
    employeeId: 1,
    employeeName: "<PERSON>",
    employeeEmail: "<EMAIL>",
    department: "Engineering",
    position: "Senior Software Engineer",
    baseSalary: 125000,
    overtime: 2500,
    bonuses: 5000,
    deductions: 1200,
    netPay: 131300,
    payPeriod: "2024-01-01 to 2024-01-31",
    status: "processed",
    processedDate: "2024-02-01",
    avatar: "/avatars/sarah.jpg"
  },
  {
    id: 2,
    employeeId: 2,
    employeeName: "Mike Chen",
    employeeEmail: "<EMAIL>",
    department: "Product",
    position: "Product Manager",
    baseSalary: 115000,
    overtime: 1800,
    bonuses: 3000,
    deductions: 1100,
    netPay: 118700,
    payPeriod: "2024-01-01 to 2024-01-31",
    status: "pending",
    processedDate: null,
    avatar: "/avatars/mike.jpg"
  },
  {
    id: 3,
    employeeId: 3,
    employeeName: "Emily Davis",
    employeeEmail: "<EMAIL>",
    department: "Design",
    position: "UX Designer",
    baseSalary: 95000,
    overtime: 1200,
    bonuses: 2000,
    deductions: 900,
    netPay: 97300,
    payPeriod: "2024-01-01 to 2024-01-31",
    status: "processed",
    processedDate: "2024-02-01",
    avatar: "/avatars/emily.jpg"
  },
  {
    id: 4,
    employeeId: 4,
    employeeName: "David Wilson",
    employeeEmail: "<EMAIL>",
    department: "Engineering",
    position: "DevOps Engineer",
    baseSalary: 110000,
    overtime: 2200,
    bonuses: 1500,
    deductions: 1000,
    netPay: 112700,
    payPeriod: "2024-01-01 to 2024-01-31",
    status: "review",
    processedDate: null,
    avatar: "/avatars/david.jpg"
  }
]

const payrollSummary = {
  totalEmployees: payrollData.length,
  totalGrossPay: payrollData.reduce((sum, p) => sum + p.baseSalary + p.overtime + p.bonuses, 0),
  totalDeductions: payrollData.reduce((sum, p) => sum + p.deductions, 0),
  totalNetPay: payrollData.reduce((sum, p) => sum + p.netPay, 0),
  processed: payrollData.filter(p => p.status === "processed").length,
  pending: payrollData.filter(p => p.status === "pending").length,
  review: payrollData.filter(p => p.status === "review").length
}

export default function PayrollPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStatus, setSelectedStatus] = useState("All")
  const [selectedDepartment, setSelectedDepartment] = useState("All")
  const [selectedPayPeriod, setSelectedPayPeriod] = useState("2024-01-01 to 2024-01-31")

  const filteredPayroll = payrollData.filter(payroll => {
    const matchesSearch = payroll.employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payroll.employeeEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         payroll.department.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesStatus = selectedStatus === "All" || payroll.status === selectedStatus
    const matchesDepartment = selectedDepartment === "All" || payroll.department === selectedDepartment
    
    return matchesSearch && matchesStatus && matchesDepartment
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "processed": return "success"
      case "pending": return "warning"
      case "review": return "destructive"
      default: return "secondary"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "processed": return <CheckCircle className="h-4 w-4" />
      case "pending": return <Clock className="h-4 w-4" />
      case "review": return <AlertCircle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <Header
        title="Payroll Management"
        subtitle={`Processing payroll for ${payrollSummary.totalEmployees} employees`}
        actions={
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Upload className="w-4 h-4 mr-2" />
              Import
            </Button>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button size="sm">
              <Calculator className="w-4 h-4 mr-2" />
              Process Payroll
            </Button>
          </div>
        }
      />

      {/* Payroll Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-8 w-8 text-green-600 dark:text-green-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">
                  ${payrollSummary.totalNetPay.toLocaleString()}
                </p>
                <p className="text-sm text-muted-foreground">Total Net Pay</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{payrollSummary.processed}</p>
                <p className="text-sm text-muted-foreground">Processed</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{payrollSummary.pending}</p>
                <p className="text-sm text-muted-foreground">Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
              <div>
                <p className="text-2xl font-bold text-foreground">{payrollSummary.review}</p>
                <p className="text-sm text-muted-foreground">Needs Review</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by employee name, email, or department..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                aria-label="Filter by status"
              >
                <option value="All">All Status</option>
                <option value="processed">Processed</option>
                <option value="pending">Pending</option>
                <option value="review">Needs Review</option>
              </select>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                aria-label="Filter by department"
              >
                <option value="All">All Departments</option>
                <option value="Engineering">Engineering</option>
                <option value="Product">Product</option>
                <option value="Design">Design</option>
                <option value="Sales">Sales</option>
                <option value="Marketing">Marketing</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredPayroll.length} of {payrollData.length} payroll records
        </p>
      </div>

      {/* Payroll Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/50 border-b border-border">
                <tr>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Employee</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Department</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Base Salary</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Overtime</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Bonuses</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Deductions</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Net Pay</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Status</th>
                  <th className="text-left py-3 px-6 font-medium text-foreground">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPayroll.map((payroll, index) => (
                  <motion.tr
                    key={payroll.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.05 }}
                    className="border-b border-border hover:bg-muted/50"
                  >
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={payroll.avatar} alt={payroll.employeeName} />
                          <AvatarFallback>
                            {payroll.employeeName.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-foreground">{payroll.employeeName}</p>
                          <p className="text-sm text-muted-foreground">{payroll.position}</p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-muted-foreground">{payroll.department}</td>
                    <td className="py-4 px-6 text-foreground font-medium">
                      ${payroll.baseSalary.toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-foreground">
                      ${payroll.overtime.toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-green-600 font-medium">
                      ${payroll.bonuses.toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-red-600">
                      -${payroll.deductions.toLocaleString()}
                    </td>
                    <td className="py-4 px-6 text-foreground font-bold">
                      ${payroll.netPay.toLocaleString()}
                    </td>
                    <td className="py-4 px-6">
                      <Badge variant={getStatusColor(payroll.status) as any} className="flex items-center space-x-1">
                        {getStatusIcon(payroll.status)}
                        <span className="ml-1">{payroll.status.charAt(0).toUpperCase() + payroll.status.slice(1)}</span>
                      </Badge>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
