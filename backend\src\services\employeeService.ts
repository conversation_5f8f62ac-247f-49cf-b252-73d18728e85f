import { DatabaseService } from './databaseService'
import { logger } from '../utils/logger'
import { v4 as uuidv4 } from 'uuid'

interface Employee {
  id: string
  firstName: string
  lastName: string
  email: string
  personalEmail?: string
  phone?: string
  dateOfBirth?: string
  gender?: string
  address?: any
  emergencyContact?: any
  departmentId: string
  positionId: string
  managerId?: string
  employeeType: string
  hireDate: string
  salary?: number
  currency?: string
  workLocation?: string
  skills?: any[]
  certifications?: any[]
  status: string
  createdAt: string
  updatedAt: string
}

export class EmployeeService {
  private db: DatabaseService

  constructor() {
    this.db = new DatabaseService()
  }

  /**
   * Get employees with filtering and pagination
   */
  async getEmployees(filters: any, user: any): Promise<any> {
    try {
      const page = parseInt(filters.page) || 1
      const limit = Math.min(parseInt(filters.limit) || 20, 100)
      const offset = (page - 1) * limit

      let whereClause = 'WHERE e.status != \'deleted\''
      const params: any[] = []
      let paramIndex = 1

      // Apply filters
      if (filters.search) {
        whereClause += ` AND (e.first_name ILIKE $${paramIndex} OR e.last_name ILIKE $${paramIndex} OR e.email ILIKE $${paramIndex})`
        params.push(`%${filters.search}%`)
        paramIndex++
      }

      if (filters.department) {
        whereClause += ` AND e.department_id = $${paramIndex}`
        params.push(filters.department)
        paramIndex++
      }

      if (filters.status) {
        whereClause += ` AND e.status = $${paramIndex}`
        params.push(filters.status)
        paramIndex++
      }

      if (filters.manager) {
        whereClause += ` AND e.manager_id = $${paramIndex}`
        params.push(filters.manager)
        paramIndex++
      }

      // Apply department restrictions for non-HR users
      if (!user.permissions.includes('hr') && !user.permissions.includes('hr_admin')) {
        if (user.permissions.includes('manager') && user.departmentId) {
          whereClause += ` AND e.department_id = $${paramIndex}`
          params.push(user.departmentId)
          paramIndex++
        } else {
          whereClause += ` AND e.id = $${paramIndex}`
          params.push(user.employeeId)
          paramIndex++
        }
      }

      // Sorting
      const sortBy = filters.sortBy || 'firstName'
      const sortOrder = filters.sortOrder === 'desc' ? 'DESC' : 'ASC'
      const orderClause = `ORDER BY e.${sortBy} ${sortOrder}`

      const query = `
        SELECT 
          e.*,
          d.name as department_name,
          p.title as position_title,
          m.first_name as manager_first_name,
          m.last_name as manager_last_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN employees m ON e.manager_id = m.id
        ${whereClause}
        ${orderClause}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `

      const countQuery = `
        SELECT COUNT(*) as total
        FROM employees e
        ${whereClause}
      `

      const [dataResult, countResult] = await Promise.all([
        this.db.query(query, [...params, limit, offset]),
        this.db.query(countQuery, params)
      ])

      const total = parseInt(countResult.rows[0].total)

      return {
        data: dataResult.rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      logger.error('Error fetching employees:', error)
      throw new Error('Failed to fetch employees')
    }
  }

  /**
   * Get employee by ID
   */
  async getEmployeeById(employeeId: string, user: any): Promise<Employee | null> {
    try {
      const query = `
        SELECT 
          e.*,
          d.name as department_name,
          p.title as position_title,
          m.first_name as manager_first_name,
          m.last_name as manager_last_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        LEFT JOIN employees m ON e.manager_id = m.id
        WHERE e.id = $1 AND e.status != 'deleted'
      `

      const result = await this.db.query(query, [employeeId])
      
      if (result.rows.length === 0) {
        return null
      }

      const employee = result.rows[0]

      // Check access permissions
      if (!this.hasEmployeeAccess(employee, user)) {
        return null
      }

      return employee
    } catch (error) {
      logger.error('Error fetching employee:', error)
      throw new Error('Failed to fetch employee')
    }
  }

  /**
   * Create new employee
   */
  async createEmployee(employeeData: any, createdBy: string): Promise<Employee> {
    try {
      const employeeId = uuidv4()
      
      const query = `
        INSERT INTO employees (
          id, first_name, last_name, email, personal_email, phone,
          date_of_birth, gender, address, emergency_contact,
          department_id, position_id, manager_id, employee_type,
          hire_date, salary, currency, work_location, skills,
          certifications, status, created_by, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14,
          $15, $16, $17, $18, $19, $20, 'active', $21, NOW(), NOW()
        ) RETURNING *
      `

      const result = await this.db.query(query, [
        employeeId,
        employeeData.firstName,
        employeeData.lastName,
        employeeData.email,
        employeeData.personalEmail,
        employeeData.phone,
        employeeData.dateOfBirth,
        employeeData.gender,
        JSON.stringify(employeeData.address),
        JSON.stringify(employeeData.emergencyContact),
        employeeData.departmentId,
        employeeData.positionId,
        employeeData.managerId,
        employeeData.employeeType,
        employeeData.hireDate,
        employeeData.salary,
        employeeData.currency,
        employeeData.workLocation,
        JSON.stringify(employeeData.skills || []),
        JSON.stringify(employeeData.certifications || []),
        createdBy
      ])

      logger.info('Employee created', { 
        employeeId,
        email: employeeData.email,
        createdBy 
      })

      return result.rows[0]
    } catch (error) {
      logger.error('Error creating employee:', error)
      throw new Error('Failed to create employee')
    }
  }

  /**
   * Update employee
   */
  async updateEmployee(employeeId: string, updateData: any, user: any): Promise<Employee | null> {
    try {
      // Check if employee exists and user has access
      const existingEmployee = await this.getEmployeeById(employeeId, user)
      if (!existingEmployee) {
        return null
      }

      // Filter allowed fields based on user permissions
      const allowedFields = this.getAllowedUpdateFields(user)
      const filteredData = Object.keys(updateData)
        .filter(key => allowedFields.includes(key))
        .reduce((obj: any, key) => {
          obj[key] = updateData[key]
          return obj
        }, {})

      if (Object.keys(filteredData).length === 0) {
        return existingEmployee
      }

      // Build update query
      const setClause = Object.keys(filteredData)
        .map((key, index) => `${this.getColumnName(key)} = $${index + 2}`)
        .join(', ')

      const query = `
        UPDATE employees 
        SET ${setClause}, updated_at = NOW(), updated_by = $1
        WHERE id = $${Object.keys(filteredData).length + 2}
        RETURNING *
      `

      const params = [user.id, ...Object.values(filteredData), employeeId]
      const result = await this.db.query(query, params)

      logger.info('Employee updated', { 
        employeeId,
        updatedBy: user.id,
        fields: Object.keys(filteredData)
      })

      return result.rows[0]
    } catch (error) {
      logger.error('Error updating employee:', error)
      throw new Error('Failed to update employee')
    }
  }

  /**
   * Deactivate employee (soft delete)
   */
  async deactivateEmployee(employeeId: string, deactivatedBy: string): Promise<boolean> {
    try {
      const query = `
        UPDATE employees 
        SET status = 'inactive', updated_at = NOW(), updated_by = $1
        WHERE id = $2 AND status != 'deleted'
      `

      const result = await this.db.query(query, [deactivatedBy, employeeId])

      if (result.rowCount === 0) {
        return false
      }

      logger.info('Employee deactivated', { 
        employeeId,
        deactivatedBy 
      })

      return true
    } catch (error) {
      logger.error('Error deactivating employee:', error)
      throw new Error('Failed to deactivate employee')
    }
  }

  /**
   * Get employee profile with additional details
   */
  async getEmployeeProfile(employeeId: string, user: any): Promise<any> {
    try {
      const employee = await this.getEmployeeById(employeeId, user)
      if (!employee) {
        return null
      }

      // Get additional profile data
      const [skillsResult, reviewsResult, goalsResult] = await Promise.all([
        this.db.query('SELECT * FROM employee_skills WHERE employee_id = $1', [employeeId]),
        this.db.query('SELECT * FROM performance_reviews WHERE employee_id = $1 ORDER BY review_date DESC LIMIT 5', [employeeId]),
        this.db.query('SELECT * FROM employee_goals WHERE employee_id = $1 AND status = \'active\'', [employeeId])
      ])

      return {
        ...employee,
        skills: skillsResult.rows,
        recentReviews: reviewsResult.rows,
        activeGoals: goalsResult.rows
      }
    } catch (error) {
      logger.error('Error fetching employee profile:', error)
      throw new Error('Failed to fetch employee profile')
    }
  }

  // Helper methods
  private hasEmployeeAccess(employee: any, user: any): boolean {
    // HR and HR Admin can access all employees
    if (user.permissions.includes('hr') || user.permissions.includes('hr_admin')) {
      return true
    }

    // Managers can access their team members
    if (user.permissions.includes('manager') && employee.department_id === user.departmentId) {
      return true
    }

    // Employees can access their own data
    if (employee.id === user.employeeId) {
      return true
    }

    return false
  }

  private getAllowedUpdateFields(user: any): string[] {
    const baseFields = ['personalEmail', 'phone', 'address', 'emergencyContact']
    
    if (user.permissions.includes('hr_admin')) {
      return [
        ...baseFields,
        'firstName', 'lastName', 'email', 'departmentId', 'positionId',
        'managerId', 'employeeType', 'salary', 'currency', 'workLocation',
        'skills', 'certifications'
      ]
    }

    if (user.permissions.includes('hr')) {
      return [
        ...baseFields,
        'departmentId', 'positionId', 'managerId', 'workLocation', 'skills'
      ]
    }

    return baseFields
  }

  private getColumnName(fieldName: string): string {
    const fieldMap: Record<string, string> = {
      firstName: 'first_name',
      lastName: 'last_name',
      personalEmail: 'personal_email',
      dateOfBirth: 'date_of_birth',
      emergencyContact: 'emergency_contact',
      departmentId: 'department_id',
      positionId: 'position_id',
      managerId: 'manager_id',
      employeeType: 'employee_type',
      hireDate: 'hire_date',
      workLocation: 'work_location'
    }

    return fieldMap[fieldName] || fieldName
  }

  /**
   * Get employee team members
   */
  async getEmployeeTeam(employeeId: string, user: any): Promise<any> {
    try {
      const query = `
        SELECT e.*, d.name as department_name, p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.manager_id = $1 AND e.status = 'active'
        ORDER BY e.first_name, e.last_name
      `
      const result = await this.db.query(query, [employeeId])
      return result.rows
    } catch (error) {
      logger.error('Error fetching employee team:', error)
      throw error
    }
  }

  /**
   * Get direct reports for an employee
   */
  async getDirectReports(employeeId: string, user: any): Promise<any> {
    try {
      const query = `
        SELECT e.*, d.name as department_name, p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.manager_id = $1 AND e.status = 'active'
        ORDER BY e.first_name, e.last_name
      `
      const result = await this.db.query(query, [employeeId])
      return result.rows
    } catch (error) {
      logger.error('Error fetching direct reports:', error)
      throw error
    }
  }

  /**
   * Add skills to an employee
   */
  async addEmployeeSkills(employeeId: string, skills: any[], user: any): Promise<any> {
    try {
      const query = `
        UPDATE employees
        SET skills = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING skills
      `
      const result = await this.db.query(query, [JSON.stringify(skills), employeeId])
      return result.rows[0]
    } catch (error) {
      logger.error('Error adding employee skills:', error)
      throw error
    }
  }

  /**
   * Get employee analytics
   */
  async getEmployeeAnalytics(filters: any, user: any): Promise<any> {
    try {
      const analytics = {
        totalEmployees: 0,
        activeEmployees: 0,
        departmentBreakdown: [],
        averageTenure: 0,
        turnoverRate: 0
      }

      // Get total and active employee counts
      const countQuery = `
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'active' THEN 1 END) as active
        FROM employees
        WHERE status != 'deleted'
      `
      const countResult = await this.db.query(countQuery)
      analytics.totalEmployees = parseInt(countResult.rows[0].total)
      analytics.activeEmployees = parseInt(countResult.rows[0].active)

      return analytics
    } catch (error) {
      logger.error('Error fetching employee analytics:', error)
      throw error
    }
  }

  /**
   * Search employees
   */
  async searchEmployees(filters: any, user: any): Promise<any> {
    try {
      const searchTerm = filters.q || ''
      const query = `
        SELECT e.*, d.name as department_name, p.title as position_title
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        LEFT JOIN positions p ON e.position_id = p.id
        WHERE e.status = 'active'
        AND (
          e.first_name ILIKE $1 OR
          e.last_name ILIKE $1 OR
          e.email ILIKE $1 OR
          e.employee_id ILIKE $1
        )
        ORDER BY e.first_name, e.last_name
        LIMIT 20
      `
      const result = await this.db.query(query, [`%${searchTerm}%`])
      return result.rows
    } catch (error) {
      logger.error('Error searching employees:', error)
      throw error
    }
  }
}
